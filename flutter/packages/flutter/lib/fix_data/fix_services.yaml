# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are from the Services library. *
version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/122446
  - title: "Migrate to empty 'text' string"
    date: 2023-06-26
    element:
      uris: [ 'services.dart' ]
      constructor: ''
      inClass: 'ClipboardData'
    changes:
      - kind: 'changeParameterType'
        name: 'text'
        nullability: 'non_null'
        argumentValue:
          expression: "''"

  # Changes made in https://github.com/flutter/flutter/pull/60320
  - title: "Migrate to 'viewId'"
    date: 2020-07-05
    element:
      uris: [ 'services.dart' ]
      getter: id
      inClass: 'TextureAndroidViewController'
    changes:
      - kind: 'rename'
        newName: 'viewId'

  # Changes made in https://github.com/flutter/flutter/pull/60320
  - title: "Migrate to 'viewId'"
    date: 2020-07-05
    element:
      uris: [ 'services.dart' ]
      getter: id
      inClass: 'SurfaceAndroidViewController'
    changes:
      - kind: 'rename'
        newName: 'viewId'

  # Changes made in https://github.com/flutter/flutter/pull/81303
  - title: "Migrate to 'setEnabledSystemUIMode'"
    date: 2021-06-08
    element:
      uris: [ 'services.dart' ]
      method: 'setEnabledSystemUIOverlays'
      inClass: 'SystemChrome'
    changes:
      - kind: 'rename'
        newName: 'setEnabledSystemUIMode'
      - kind: 'removeParameter'
        index: 0
      - kind: 'addParameter'
        index: 0
        name: 'mode'
        style: required_positional
        argumentValue:
          expression: 'SystemUiMode.manual'
      - kind: 'addParameter'
        index: 1
        name: 'overlays'
        style: optional_named
        argumentValue:
          expression: '{% overlays %}'
          requiredIf: "overlays != ''"
    variables:
      overlays:
        kind: 'fragment'
        value: 'arguments[0]'

  # Changes made in https://github.com/flutter/flutter/pull/161518
  - title: "Migrate 'SelectionChangedCause.scribble' to 'SelectionChangedCause.stylusHandwriting'"
    date: 2025-01-13
    element:
      uris: [ 'services.dart' ]
      constant: 'scribble'
      inEnum: 'SelectionChangedCause'
    changes:
      - kind: 'replacedBy'
        newElement:
          uris: [ 'services.dart' ]
          constant: 'stylusHandwriting'
          inEnum: 'SelectionChangedCause'

# Before adding a new fix: read instructions at the top of this file.
