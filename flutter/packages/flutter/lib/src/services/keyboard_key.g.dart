// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// @docImport 'package:flutter/widgets.dart';
///
/// @docImport 'raw_keyboard.dart';
library;

import 'package:flutter/foundation.dart';

export 'package:flutter/foundation.dart' show DiagnosticPropertiesBuilder;

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by dev/tools/gen_keycodes/bin/gen_keycodes.dart and
// should not be edited directly.
//
// Edit the template dev/tools/gen_keycodes/data/keyboard_key.tmpl instead.
// See dev/tools/gen_keycodes/README.md for more information.

/// A base class for all keyboard key types.
///
/// See also:
///
///  * [PhysicalKeyboardKey], a class with static values that describe the keys
///    that are returned from [RawKeyEvent.physicalKey].
///  * [LogicalKeyboardKey], a class with static values that describe the keys
///    that are returned from [RawKeyEvent.logicalKey].
abstract class KeyboardKey with Diagnosticable {
  /// Abstract const constructor. This constructor enables subclasses to provide
  /// const constructors so that they can be used in const expressions.
  const KeyboardKey();
}

/// A class with static values that describe the keys that are returned from
/// [RawKeyEvent.logicalKey].
///
/// These represent *logical* keys, which are keys which are interpreted in the
/// context of any modifiers, modes, or keyboard layouts which may be in effect.
///
/// This is contrast to [PhysicalKeyboardKey], which represents a physical key
/// in a particular location on the keyboard, without regard for the modifier
/// state, mode, or keyboard layout.
///
/// As an example, if you wanted to implement an app where the "Q" key "quit"
/// something, you'd want to look at the logical key to detect this, since you
/// would like to have it match the key with "Q" on it, instead of always
/// looking for "the key next to the TAB key", since on a French keyboard,
/// the key next to the TAB key has an "A" on it.
///
/// Conversely, if you wanted a game where the key next to the CAPS LOCK (the
/// "A" key on a QWERTY keyboard) moved the player to the left, you'd want to
/// look at the physical key to make sure that regardless of the character the
/// key produces, you got the key that is in that location on the keyboard.
///
/// {@tool dartpad}
/// This example shows how to detect if the user has selected the logical "Q"
/// key and handle the key if they have.
///
/// ** See code in examples/api/lib/services/keyboard_key/logical_keyboard_key.0.dart **
/// {@end-tool}
/// See also:
///
///  * [RawKeyEvent], the keyboard event object received by widgets that listen
///    to keyboard events.
///  * [Focus.onKey], the handler on a widget that lets you handle key events.
///  * [RawKeyboardListener], a widget used to listen to keyboard events (but
///    not handle them).
@immutable
class LogicalKeyboardKey extends KeyboardKey {
  /// Creates a new LogicalKeyboardKey object for a key ID.
  const LogicalKeyboardKey(this.keyId);

  /// A unique code representing this key.
  ///
  /// This is an opaque code. It should not be unpacked to derive information
  /// from it, as the representation of the code could change at any time.
  final int keyId;

  // Returns the bits that are not included in [valueMask], shifted to the
  // right.
  //
  // For example, if the input is 0x12abcdabcd, then the result is 0x12.
  //
  // This is mostly equivalent to a right shift, resolving the problem that
  // JavaScript only support 32-bit bitwise operation and needs to use division
  // instead.
  static int _nonValueBits(int n) {
    // `n >> valueMaskWidth` is equivalent to `n / divisorForValueMask`.
    const int divisorForValueMask = valueMask + 1;
    const int valueMaskWidth = 32;

    // Equivalent to assert(divisorForValueMask == (1 << valueMaskWidth)).
    const int firstDivisorWidth = 28;
    assert(
      divisorForValueMask == (1 << firstDivisorWidth) * (1 << (valueMaskWidth - firstDivisorWidth)),
    );

    // JS only supports up to 2^53 - 1, therefore non-value bits can only
    // contain (maxSafeIntegerWidth - valueMaskWidth) bits.
    const int maxSafeIntegerWidth = 52;
    const int nonValueMask = (1 << (maxSafeIntegerWidth - valueMaskWidth)) - 1;

    if (kIsWeb) {
      return (n / divisorForValueMask).floor() & nonValueMask;
    } else {
      return (n >> valueMaskWidth) & nonValueMask;
    }
  }

  static String? _unicodeKeyLabel(int keyId) {
    if (_nonValueBits(keyId) == 0) {
      return String.fromCharCode(keyId).toUpperCase();
    }
    return null;
  }

  /// A description representing the character produced by a [RawKeyEvent].
  ///
  /// This value is useful for providing readable strings for keys or keyboard
  /// shortcuts. Do not use this value to compare equality of keys; compare
  /// [keyId] instead.
  ///
  /// For printable keys, this is usually the printable character in upper case
  /// ignoring modifiers or combining keys, such as 'A', '1', or '/'. This
  /// might also return accented letters (such as 'Ù') for keys labeled as so,
  /// but not if such character is a result from preceding combining keys ('`̀'
  /// followed by key U).
  ///
  /// For other keys, [keyLabel] looks up the full key name from a predefined
  /// map, such as 'F1', 'Shift Left', or 'Media Down'. This value is an empty
  /// string if there's no key label data for a key.
  ///
  /// For the printable representation that takes into consideration the
  /// modifiers and combining keys, see [RawKeyEvent.character].
  ///
  /// {@macro flutter.services.RawKeyEventData.keyLabel}
  String get keyLabel {
    return _unicodeKeyLabel(keyId) ?? _keyLabels[keyId] ?? '';
  }

  /// The debug string to print for this keyboard key, which will be null in
  /// release mode.
  ///
  /// For printable keys, this is usually a more descriptive name related to
  /// [keyLabel], such as 'Key A', 'Digit 1', 'Backslash'. This might
  /// also return accented letters (such as 'Key Ù') for keys labeled as so.
  ///
  /// For other keys, this looks up the full key name from a predefined map (the
  /// same value as [keyLabel]), such as 'F1', 'Shift Left', or 'Media Down'. If
  /// there's no key label data for a key, this returns a name that explains the
  /// ID (such as 'Key with ID 0x00100012345').
  String? get debugName {
    String? result;
    assert(() {
      result = _keyLabels[keyId];
      if (result == null) {
        final String? unicodeKeyLabel = _unicodeKeyLabel(keyId);
        if (unicodeKeyLabel != null) {
          result = 'Key $unicodeKeyLabel';
        } else {
          result = 'Key with ID 0x${keyId.toRadixString(16).padLeft(11, '0')}';
        }
      }
      return true;
    }());
    return result;
  }

  @override
  int get hashCode => keyId.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other.runtimeType != runtimeType) {
      return false;
    }
    return other is LogicalKeyboardKey && other.keyId == keyId;
  }

  /// Returns the [LogicalKeyboardKey] constant that matches the given ID, or
  /// null, if not found.
  static LogicalKeyboardKey? findKeyByKeyId(int keyId) => _knownLogicalKeys[keyId];

  /// Returns true if the given label represents a Unicode control character.
  ///
  /// Examples of control characters are characters like "U+000A LINE FEED (LF)"
  /// or "U+001B ESCAPE (ESC)".
  ///
  /// See <https://en.wikipedia.org/wiki/Unicode_control_characters> for more
  /// information.
  ///
  /// Used by [RawKeyEvent] subclasses to help construct IDs.
  static bool isControlCharacter(String label) {
    if (label.length != 1) {
      return false;
    }
    final int codeUnit = label.codeUnitAt(0);
    return (codeUnit <= 0x1f && codeUnit >= 0x00) || (codeUnit >= 0x7f && codeUnit <= 0x9f);
  }

  /// Returns true if the [keyId] of this object is one that is auto-generated by
  /// Flutter.
  ///
  /// Auto-generated key IDs are generated in response to platform key codes
  /// which Flutter doesn't recognize, and their IDs shouldn't be used in a
  /// persistent way.
  ///
  /// Auto-generated IDs should be a rare occurrence: Flutter supports most keys.
  ///
  /// Keys that generate Unicode characters (even if unknown to Flutter) will
  /// not return true for [isAutogenerated], since they will be assigned a
  /// Unicode-based code that will remain stable.
  ///
  /// If Flutter adds support for a previously unsupported key code, the ID it
  /// reports will change, but the ID will remain stable on the platform it is
  /// produced on until Flutter adds support for recognizing it.
  ///
  /// So, hypothetically, if Android added a new key code of 0xffff,
  /// representing a new "do what I mean" key, then the auto-generated code
  /// would be 0x1020000ffff, but once Flutter added the "doWhatIMean" key to
  /// the definitions below, the new code would be 0x0020000ffff for all
  /// platforms that had a "do what I mean" key from then on.
  bool get isAutogenerated => (keyId & planeMask) >= startOfPlatformPlanes;

  /// Returns a set of pseudo-key synonyms for the given `key`.
  ///
  /// This allows finding the pseudo-keys that also represent a concrete `key`
  /// so that a class with a key map can match pseudo-keys as well as the actual
  /// generated keys.
  ///
  /// Pseudo-keys returned in the set are typically used to represent keys which
  /// appear in multiple places on the keyboard, such as the [shift], [alt],
  /// [control], and [meta] keys. Pseudo-keys in the returned set won't ever be
  /// generated directly, but if a more specific key event is received, then
  /// this set can be used to find the more general pseudo-key. For example, if
  /// this is a [shiftLeft] key, this accessor will return the set
  /// `<LogicalKeyboardKey>{ shift }`.
  Set<LogicalKeyboardKey> get synonyms => _synonyms[this] ?? <LogicalKeyboardKey>{};

  /// Takes a set of keys, and returns the same set, but with any keys that have
  /// synonyms replaced.
  ///
  /// It is used, for example, to take sets of keys with members like
  /// [controlRight] and [controlLeft] and convert that set to contain just
  /// [control], so that the question "is any control key down?" can be asked.
  static Set<LogicalKeyboardKey> collapseSynonyms(Set<LogicalKeyboardKey> input) {
    return input.expand((LogicalKeyboardKey element) {
      return _synonyms[element] ?? <LogicalKeyboardKey>{element};
    }).toSet();
  }

  /// Returns the given set with any pseudo-keys expanded into their synonyms.
  ///
  /// It is used, for example, to take sets of keys with members like [control]
  /// and [shift] and convert that set to contain [controlLeft], [controlRight],
  /// [shiftLeft], and [shiftRight].
  static Set<LogicalKeyboardKey> expandSynonyms(Set<LogicalKeyboardKey> input) {
    return input.expand((LogicalKeyboardKey element) {
      return _reverseSynonyms[element] ?? <LogicalKeyboardKey>{element};
    }).toSet();
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(StringProperty('keyId', '0x${keyId.toRadixString(16).padLeft(8, '0')}'));
    properties.add(StringProperty('keyLabel', keyLabel));
    properties.add(StringProperty('debugName', debugName, defaultValue: null));
  }

  /// Mask for the 32-bit value portion of the key code.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int valueMask = 0x000ffffffff;

  /// Mask for the plane prefix portion of the key code.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int planeMask = 0x0ff00000000;

  /// The plane value for keys which have a Unicode representation.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int unicodePlane = 0x00000000000;

  /// The plane value for keys defined by Chromium and does not have a Unicode
  /// representation.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int unprintablePlane = 0x00100000000;

  /// The plane value for keys defined by Flutter.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int flutterPlane = 0x00200000000;

  /// The platform plane with the lowest mask value, beyond which the keys are
  /// considered autogenerated.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int startOfPlatformPlanes = 0x01100000000;

  /// The plane value for the private keys defined by the Android embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int androidPlane = 0x01100000000;

  /// The plane value for the private keys defined by the Fuchsia embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int fuchsiaPlane = 0x01200000000;

  /// The plane value for the private keys defined by the iOS embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int iosPlane = 0x01300000000;

  /// The plane value for the private keys defined by the macOS embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int macosPlane = 0x01400000000;

  /// The plane value for the private keys defined by the Gtk embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int gtkPlane = 0x01500000000;

  /// The plane value for the private keys defined by the Windows embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int windowsPlane = 0x01600000000;

  /// The plane value for the private keys defined by the Web embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int webPlane = 0x01700000000;

  /// The plane value for the private keys defined by the GLFW embedding.
  ///
  /// This is used by platform-specific code to generate Flutter key codes.
  static const int glfwPlane = 0x01800000000;

  /// Represents the logical "Space" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey space = LogicalKeyboardKey(0x00000000020);

  /// Represents the logical "Exclamation" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey exclamation = LogicalKeyboardKey(0x00000000021);

  /// Represents the logical "Quote" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey quote = LogicalKeyboardKey(0x00000000022);

  /// Represents the logical "Number Sign" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numberSign = LogicalKeyboardKey(0x00000000023);

  /// Represents the logical "Dollar" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey dollar = LogicalKeyboardKey(0x00000000024);

  /// Represents the logical "Percent" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey percent = LogicalKeyboardKey(0x00000000025);

  /// Represents the logical "Ampersand" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey ampersand = LogicalKeyboardKey(0x00000000026);

  /// Represents the logical "Quote Single" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey quoteSingle = LogicalKeyboardKey(0x00000000027);

  /// Represents the logical "Parenthesis Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey parenthesisLeft = LogicalKeyboardKey(0x00000000028);

  /// Represents the logical "Parenthesis Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey parenthesisRight = LogicalKeyboardKey(0x00000000029);

  /// Represents the logical "Asterisk" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey asterisk = LogicalKeyboardKey(0x0000000002a);

  /// Represents the logical "Add" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey add = LogicalKeyboardKey(0x0000000002b);

  /// Represents the logical "Comma" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey comma = LogicalKeyboardKey(0x0000000002c);

  /// Represents the logical "Minus" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey minus = LogicalKeyboardKey(0x0000000002d);

  /// Represents the logical "Period" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey period = LogicalKeyboardKey(0x0000000002e);

  /// Represents the logical "Slash" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey slash = LogicalKeyboardKey(0x0000000002f);

  /// Represents the logical "Digit 0" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit0 = LogicalKeyboardKey(0x00000000030);

  /// Represents the logical "Digit 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit1 = LogicalKeyboardKey(0x00000000031);

  /// Represents the logical "Digit 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit2 = LogicalKeyboardKey(0x00000000032);

  /// Represents the logical "Digit 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit3 = LogicalKeyboardKey(0x00000000033);

  /// Represents the logical "Digit 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit4 = LogicalKeyboardKey(0x00000000034);

  /// Represents the logical "Digit 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit5 = LogicalKeyboardKey(0x00000000035);

  /// Represents the logical "Digit 6" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit6 = LogicalKeyboardKey(0x00000000036);

  /// Represents the logical "Digit 7" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit7 = LogicalKeyboardKey(0x00000000037);

  /// Represents the logical "Digit 8" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit8 = LogicalKeyboardKey(0x00000000038);

  /// Represents the logical "Digit 9" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey digit9 = LogicalKeyboardKey(0x00000000039);

  /// Represents the logical "Colon" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colon = LogicalKeyboardKey(0x0000000003a);

  /// Represents the logical "Semicolon" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey semicolon = LogicalKeyboardKey(0x0000000003b);

  /// Represents the logical "Less" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey less = LogicalKeyboardKey(0x0000000003c);

  /// Represents the logical "Equal" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey equal = LogicalKeyboardKey(0x0000000003d);

  /// Represents the logical "Greater" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey greater = LogicalKeyboardKey(0x0000000003e);

  /// Represents the logical "Question" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey question = LogicalKeyboardKey(0x0000000003f);

  /// Represents the logical "At" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey at = LogicalKeyboardKey(0x00000000040);

  /// Represents the logical "Bracket Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey bracketLeft = LogicalKeyboardKey(0x0000000005b);

  /// Represents the logical "Backslash" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey backslash = LogicalKeyboardKey(0x0000000005c);

  /// Represents the logical "Bracket Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey bracketRight = LogicalKeyboardKey(0x0000000005d);

  /// Represents the logical "Caret" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey caret = LogicalKeyboardKey(0x0000000005e);

  /// Represents the logical "Underscore" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey underscore = LogicalKeyboardKey(0x0000000005f);

  /// Represents the logical "Backquote" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey backquote = LogicalKeyboardKey(0x00000000060);

  /// Represents the logical "Key A" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyA = LogicalKeyboardKey(0x00000000061);

  /// Represents the logical "Key B" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyB = LogicalKeyboardKey(0x00000000062);

  /// Represents the logical "Key C" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyC = LogicalKeyboardKey(0x00000000063);

  /// Represents the logical "Key D" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyD = LogicalKeyboardKey(0x00000000064);

  /// Represents the logical "Key E" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyE = LogicalKeyboardKey(0x00000000065);

  /// Represents the logical "Key F" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyF = LogicalKeyboardKey(0x00000000066);

  /// Represents the logical "Key G" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyG = LogicalKeyboardKey(0x00000000067);

  /// Represents the logical "Key H" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyH = LogicalKeyboardKey(0x00000000068);

  /// Represents the logical "Key I" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyI = LogicalKeyboardKey(0x00000000069);

  /// Represents the logical "Key J" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyJ = LogicalKeyboardKey(0x0000000006a);

  /// Represents the logical "Key K" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyK = LogicalKeyboardKey(0x0000000006b);

  /// Represents the logical "Key L" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyL = LogicalKeyboardKey(0x0000000006c);

  /// Represents the logical "Key M" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyM = LogicalKeyboardKey(0x0000000006d);

  /// Represents the logical "Key N" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyN = LogicalKeyboardKey(0x0000000006e);

  /// Represents the logical "Key O" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyO = LogicalKeyboardKey(0x0000000006f);

  /// Represents the logical "Key P" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyP = LogicalKeyboardKey(0x00000000070);

  /// Represents the logical "Key Q" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyQ = LogicalKeyboardKey(0x00000000071);

  /// Represents the logical "Key R" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyR = LogicalKeyboardKey(0x00000000072);

  /// Represents the logical "Key S" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyS = LogicalKeyboardKey(0x00000000073);

  /// Represents the logical "Key T" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyT = LogicalKeyboardKey(0x00000000074);

  /// Represents the logical "Key U" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyU = LogicalKeyboardKey(0x00000000075);

  /// Represents the logical "Key V" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyV = LogicalKeyboardKey(0x00000000076);

  /// Represents the logical "Key W" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyW = LogicalKeyboardKey(0x00000000077);

  /// Represents the logical "Key X" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyX = LogicalKeyboardKey(0x00000000078);

  /// Represents the logical "Key Y" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyY = LogicalKeyboardKey(0x00000000079);

  /// Represents the logical "Key Z" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey keyZ = LogicalKeyboardKey(0x0000000007a);

  /// Represents the logical "Brace Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey braceLeft = LogicalKeyboardKey(0x0000000007b);

  /// Represents the logical "Bar" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey bar = LogicalKeyboardKey(0x0000000007c);

  /// Represents the logical "Brace Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey braceRight = LogicalKeyboardKey(0x0000000007d);

  /// Represents the logical "Tilde" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tilde = LogicalKeyboardKey(0x0000000007e);

  /// Represents the logical "Unidentified" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey unidentified = LogicalKeyboardKey(0x00100000001);

  /// Represents the logical "Backspace" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey backspace = LogicalKeyboardKey(0x00100000008);

  /// Represents the logical "Tab" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tab = LogicalKeyboardKey(0x00100000009);

  /// Represents the logical "Enter" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey enter = LogicalKeyboardKey(0x0010000000d);

  /// Represents the logical "Escape" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey escape = LogicalKeyboardKey(0x0010000001b);

  /// Represents the logical "Delete" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey delete = LogicalKeyboardKey(0x0010000007f);

  /// Represents the logical "Accel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey accel = LogicalKeyboardKey(0x00100000101);

  /// Represents the logical "Alt Graph" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey altGraph = LogicalKeyboardKey(0x00100000103);

  /// Represents the logical "Caps Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey capsLock = LogicalKeyboardKey(0x00100000104);

  /// Represents the logical "Fn" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey fn = LogicalKeyboardKey(0x00100000106);

  /// Represents the logical "Fn Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey fnLock = LogicalKeyboardKey(0x00100000107);

  /// Represents the logical "Hyper" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hyper = LogicalKeyboardKey(0x00100000108);

  /// Represents the logical "Num Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numLock = LogicalKeyboardKey(0x0010000010a);

  /// Represents the logical "Scroll Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey scrollLock = LogicalKeyboardKey(0x0010000010c);

  /// Represents the logical "Super" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey superKey = LogicalKeyboardKey(0x0010000010e);

  /// Represents the logical "Symbol" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey symbol = LogicalKeyboardKey(0x0010000010f);

  /// Represents the logical "Symbol Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey symbolLock = LogicalKeyboardKey(0x00100000110);

  /// Represents the logical "Shift Level 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey shiftLevel5 = LogicalKeyboardKey(0x00100000111);

  /// Represents the logical "Arrow Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey arrowDown = LogicalKeyboardKey(0x00100000301);

  /// Represents the logical "Arrow Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey arrowLeft = LogicalKeyboardKey(0x00100000302);

  /// Represents the logical "Arrow Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey arrowRight = LogicalKeyboardKey(0x00100000303);

  /// Represents the logical "Arrow Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey arrowUp = LogicalKeyboardKey(0x00100000304);

  /// Represents the logical "End" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey end = LogicalKeyboardKey(0x00100000305);

  /// Represents the logical "Home" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey home = LogicalKeyboardKey(0x00100000306);

  /// Represents the logical "Page Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pageDown = LogicalKeyboardKey(0x00100000307);

  /// Represents the logical "Page Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pageUp = LogicalKeyboardKey(0x00100000308);

  /// Represents the logical "Clear" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey clear = LogicalKeyboardKey(0x00100000401);

  /// Represents the logical "Copy" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey copy = LogicalKeyboardKey(0x00100000402);

  /// Represents the logical "Cr Sel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey crSel = LogicalKeyboardKey(0x00100000403);

  /// Represents the logical "Cut" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey cut = LogicalKeyboardKey(0x00100000404);

  /// Represents the logical "Erase Eof" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey eraseEof = LogicalKeyboardKey(0x00100000405);

  /// Represents the logical "Ex Sel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey exSel = LogicalKeyboardKey(0x00100000406);

  /// Represents the logical "Insert" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey insert = LogicalKeyboardKey(0x00100000407);

  /// Represents the logical "Paste" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey paste = LogicalKeyboardKey(0x00100000408);

  /// Represents the logical "Redo" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey redo = LogicalKeyboardKey(0x00100000409);

  /// Represents the logical "Undo" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey undo = LogicalKeyboardKey(0x0010000040a);

  /// Represents the logical "Accept" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey accept = LogicalKeyboardKey(0x00100000501);

  /// Represents the logical "Again" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey again = LogicalKeyboardKey(0x00100000502);

  /// Represents the logical "Attn" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey attn = LogicalKeyboardKey(0x00100000503);

  /// Represents the logical "Cancel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey cancel = LogicalKeyboardKey(0x00100000504);

  /// Represents the logical "Context Menu" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey contextMenu = LogicalKeyboardKey(0x00100000505);

  /// Represents the logical "Execute" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey execute = LogicalKeyboardKey(0x00100000506);

  /// Represents the logical "Find" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey find = LogicalKeyboardKey(0x00100000507);

  /// Represents the logical "Help" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey help = LogicalKeyboardKey(0x00100000508);

  /// Represents the logical "Pause" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pause = LogicalKeyboardKey(0x00100000509);

  /// Represents the logical "Play" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey play = LogicalKeyboardKey(0x0010000050a);

  /// Represents the logical "Props" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey props = LogicalKeyboardKey(0x0010000050b);

  /// Represents the logical "Select" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey select = LogicalKeyboardKey(0x0010000050c);

  /// Represents the logical "Zoom In" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey zoomIn = LogicalKeyboardKey(0x0010000050d);

  /// Represents the logical "Zoom Out" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey zoomOut = LogicalKeyboardKey(0x0010000050e);

  /// Represents the logical "Brightness Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey brightnessDown = LogicalKeyboardKey(0x00100000601);

  /// Represents the logical "Brightness Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey brightnessUp = LogicalKeyboardKey(0x00100000602);

  /// Represents the logical "Camera" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey camera = LogicalKeyboardKey(0x00100000603);

  /// Represents the logical "Eject" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey eject = LogicalKeyboardKey(0x00100000604);

  /// Represents the logical "Log Off" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey logOff = LogicalKeyboardKey(0x00100000605);

  /// Represents the logical "Power" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey power = LogicalKeyboardKey(0x00100000606);

  /// Represents the logical "Power Off" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey powerOff = LogicalKeyboardKey(0x00100000607);

  /// Represents the logical "Print Screen" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey printScreen = LogicalKeyboardKey(0x00100000608);

  /// Represents the logical "Hibernate" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hibernate = LogicalKeyboardKey(0x00100000609);

  /// Represents the logical "Standby" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey standby = LogicalKeyboardKey(0x0010000060a);

  /// Represents the logical "Wake Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey wakeUp = LogicalKeyboardKey(0x0010000060b);

  /// Represents the logical "All Candidates" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey allCandidates = LogicalKeyboardKey(0x00100000701);

  /// Represents the logical "Alphanumeric" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey alphanumeric = LogicalKeyboardKey(0x00100000702);

  /// Represents the logical "Code Input" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey codeInput = LogicalKeyboardKey(0x00100000703);

  /// Represents the logical "Compose" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey compose = LogicalKeyboardKey(0x00100000704);

  /// Represents the logical "Convert" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey convert = LogicalKeyboardKey(0x00100000705);

  /// Represents the logical "Final Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey finalMode = LogicalKeyboardKey(0x00100000706);

  /// Represents the logical "Group First" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey groupFirst = LogicalKeyboardKey(0x00100000707);

  /// Represents the logical "Group Last" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey groupLast = LogicalKeyboardKey(0x00100000708);

  /// Represents the logical "Group Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey groupNext = LogicalKeyboardKey(0x00100000709);

  /// Represents the logical "Group Previous" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey groupPrevious = LogicalKeyboardKey(0x0010000070a);

  /// Represents the logical "Mode Change" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey modeChange = LogicalKeyboardKey(0x0010000070b);

  /// Represents the logical "Next Candidate" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey nextCandidate = LogicalKeyboardKey(0x0010000070c);

  /// Represents the logical "Non Convert" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey nonConvert = LogicalKeyboardKey(0x0010000070d);

  /// Represents the logical "Previous Candidate" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey previousCandidate = LogicalKeyboardKey(0x0010000070e);

  /// Represents the logical "Process" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey process = LogicalKeyboardKey(0x0010000070f);

  /// Represents the logical "Single Candidate" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey singleCandidate = LogicalKeyboardKey(0x00100000710);

  /// Represents the logical "Hangul Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hangulMode = LogicalKeyboardKey(0x00100000711);

  /// Represents the logical "Hanja Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hanjaMode = LogicalKeyboardKey(0x00100000712);

  /// Represents the logical "Junja Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey junjaMode = LogicalKeyboardKey(0x00100000713);

  /// Represents the logical "Eisu" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey eisu = LogicalKeyboardKey(0x00100000714);

  /// Represents the logical "Hankaku" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hankaku = LogicalKeyboardKey(0x00100000715);

  /// Represents the logical "Hiragana" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hiragana = LogicalKeyboardKey(0x00100000716);

  /// Represents the logical "Hiragana Katakana" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey hiraganaKatakana = LogicalKeyboardKey(0x00100000717);

  /// Represents the logical "Kana Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey kanaMode = LogicalKeyboardKey(0x00100000718);

  /// Represents the logical "Kanji Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey kanjiMode = LogicalKeyboardKey(0x00100000719);

  /// Represents the logical "Katakana" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey katakana = LogicalKeyboardKey(0x0010000071a);

  /// Represents the logical "Romaji" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey romaji = LogicalKeyboardKey(0x0010000071b);

  /// Represents the logical "Zenkaku" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey zenkaku = LogicalKeyboardKey(0x0010000071c);

  /// Represents the logical "Zenkaku Hankaku" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey zenkakuHankaku = LogicalKeyboardKey(0x0010000071d);

  /// Represents the logical "F1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f1 = LogicalKeyboardKey(0x00100000801);

  /// Represents the logical "F2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f2 = LogicalKeyboardKey(0x00100000802);

  /// Represents the logical "F3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f3 = LogicalKeyboardKey(0x00100000803);

  /// Represents the logical "F4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f4 = LogicalKeyboardKey(0x00100000804);

  /// Represents the logical "F5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f5 = LogicalKeyboardKey(0x00100000805);

  /// Represents the logical "F6" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f6 = LogicalKeyboardKey(0x00100000806);

  /// Represents the logical "F7" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f7 = LogicalKeyboardKey(0x00100000807);

  /// Represents the logical "F8" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f8 = LogicalKeyboardKey(0x00100000808);

  /// Represents the logical "F9" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f9 = LogicalKeyboardKey(0x00100000809);

  /// Represents the logical "F10" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f10 = LogicalKeyboardKey(0x0010000080a);

  /// Represents the logical "F11" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f11 = LogicalKeyboardKey(0x0010000080b);

  /// Represents the logical "F12" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f12 = LogicalKeyboardKey(0x0010000080c);

  /// Represents the logical "F13" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f13 = LogicalKeyboardKey(0x0010000080d);

  /// Represents the logical "F14" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f14 = LogicalKeyboardKey(0x0010000080e);

  /// Represents the logical "F15" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f15 = LogicalKeyboardKey(0x0010000080f);

  /// Represents the logical "F16" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f16 = LogicalKeyboardKey(0x00100000810);

  /// Represents the logical "F17" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f17 = LogicalKeyboardKey(0x00100000811);

  /// Represents the logical "F18" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f18 = LogicalKeyboardKey(0x00100000812);

  /// Represents the logical "F19" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f19 = LogicalKeyboardKey(0x00100000813);

  /// Represents the logical "F20" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f20 = LogicalKeyboardKey(0x00100000814);

  /// Represents the logical "F21" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f21 = LogicalKeyboardKey(0x00100000815);

  /// Represents the logical "F22" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f22 = LogicalKeyboardKey(0x00100000816);

  /// Represents the logical "F23" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f23 = LogicalKeyboardKey(0x00100000817);

  /// Represents the logical "F24" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey f24 = LogicalKeyboardKey(0x00100000818);

  /// Represents the logical "Soft 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft1 = LogicalKeyboardKey(0x00100000901);

  /// Represents the logical "Soft 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft2 = LogicalKeyboardKey(0x00100000902);

  /// Represents the logical "Soft 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft3 = LogicalKeyboardKey(0x00100000903);

  /// Represents the logical "Soft 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft4 = LogicalKeyboardKey(0x00100000904);

  /// Represents the logical "Soft 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft5 = LogicalKeyboardKey(0x00100000905);

  /// Represents the logical "Soft 6" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft6 = LogicalKeyboardKey(0x00100000906);

  /// Represents the logical "Soft 7" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft7 = LogicalKeyboardKey(0x00100000907);

  /// Represents the logical "Soft 8" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey soft8 = LogicalKeyboardKey(0x00100000908);

  /// Represents the logical "Close" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey close = LogicalKeyboardKey(0x00100000a01);

  /// Represents the logical "Mail Forward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mailForward = LogicalKeyboardKey(0x00100000a02);

  /// Represents the logical "Mail Reply" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mailReply = LogicalKeyboardKey(0x00100000a03);

  /// Represents the logical "Mail Send" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mailSend = LogicalKeyboardKey(0x00100000a04);

  /// Represents the logical "Media Play Pause" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaPlayPause = LogicalKeyboardKey(0x00100000a05);

  /// Represents the logical "Media Stop" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaStop = LogicalKeyboardKey(0x00100000a07);

  /// Represents the logical "Media Track Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaTrackNext = LogicalKeyboardKey(0x00100000a08);

  /// Represents the logical "Media Track Previous" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaTrackPrevious = LogicalKeyboardKey(0x00100000a09);

  /// Represents the logical "New" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey newKey = LogicalKeyboardKey(0x00100000a0a);

  /// Represents the logical "Open" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey open = LogicalKeyboardKey(0x00100000a0b);

  /// Represents the logical "Print" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey print = LogicalKeyboardKey(0x00100000a0c);

  /// Represents the logical "Save" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey save = LogicalKeyboardKey(0x00100000a0d);

  /// Represents the logical "Spell Check" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey spellCheck = LogicalKeyboardKey(0x00100000a0e);

  /// Represents the logical "Audio Volume Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioVolumeDown = LogicalKeyboardKey(0x00100000a0f);

  /// Represents the logical "Audio Volume Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioVolumeUp = LogicalKeyboardKey(0x00100000a10);

  /// Represents the logical "Audio Volume Mute" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioVolumeMute = LogicalKeyboardKey(0x00100000a11);

  /// Represents the logical "Launch Application 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchApplication2 = LogicalKeyboardKey(0x00100000b01);

  /// Represents the logical "Launch Calendar" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchCalendar = LogicalKeyboardKey(0x00100000b02);

  /// Represents the logical "Launch Mail" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchMail = LogicalKeyboardKey(0x00100000b03);

  /// Represents the logical "Launch Media Player" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchMediaPlayer = LogicalKeyboardKey(0x00100000b04);

  /// Represents the logical "Launch Music Player" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchMusicPlayer = LogicalKeyboardKey(0x00100000b05);

  /// Represents the logical "Launch Application 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchApplication1 = LogicalKeyboardKey(0x00100000b06);

  /// Represents the logical "Launch Screen Saver" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchScreenSaver = LogicalKeyboardKey(0x00100000b07);

  /// Represents the logical "Launch Spreadsheet" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchSpreadsheet = LogicalKeyboardKey(0x00100000b08);

  /// Represents the logical "Launch Web Browser" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchWebBrowser = LogicalKeyboardKey(0x00100000b09);

  /// Represents the logical "Launch Web Cam" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchWebCam = LogicalKeyboardKey(0x00100000b0a);

  /// Represents the logical "Launch Word Processor" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchWordProcessor = LogicalKeyboardKey(0x00100000b0b);

  /// Represents the logical "Launch Contacts" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchContacts = LogicalKeyboardKey(0x00100000b0c);

  /// Represents the logical "Launch Phone" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchPhone = LogicalKeyboardKey(0x00100000b0d);

  /// Represents the logical "Launch Assistant" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchAssistant = LogicalKeyboardKey(0x00100000b0e);

  /// Represents the logical "Launch Control Panel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey launchControlPanel = LogicalKeyboardKey(0x00100000b0f);

  /// Represents the logical "Browser Back" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserBack = LogicalKeyboardKey(0x00100000c01);

  /// Represents the logical "Browser Favorites" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserFavorites = LogicalKeyboardKey(0x00100000c02);

  /// Represents the logical "Browser Forward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserForward = LogicalKeyboardKey(0x00100000c03);

  /// Represents the logical "Browser Home" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserHome = LogicalKeyboardKey(0x00100000c04);

  /// Represents the logical "Browser Refresh" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserRefresh = LogicalKeyboardKey(0x00100000c05);

  /// Represents the logical "Browser Search" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserSearch = LogicalKeyboardKey(0x00100000c06);

  /// Represents the logical "Browser Stop" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey browserStop = LogicalKeyboardKey(0x00100000c07);

  /// Represents the logical "Audio Balance Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioBalanceLeft = LogicalKeyboardKey(0x00100000d01);

  /// Represents the logical "Audio Balance Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioBalanceRight = LogicalKeyboardKey(0x00100000d02);

  /// Represents the logical "Audio Bass Boost Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioBassBoostDown = LogicalKeyboardKey(0x00100000d03);

  /// Represents the logical "Audio Bass Boost Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioBassBoostUp = LogicalKeyboardKey(0x00100000d04);

  /// Represents the logical "Audio Fader Front" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioFaderFront = LogicalKeyboardKey(0x00100000d05);

  /// Represents the logical "Audio Fader Rear" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioFaderRear = LogicalKeyboardKey(0x00100000d06);

  /// Represents the logical "Audio Surround Mode Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioSurroundModeNext = LogicalKeyboardKey(0x00100000d07);

  /// Represents the logical "AVR Input" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey avrInput = LogicalKeyboardKey(0x00100000d08);

  /// Represents the logical "AVR Power" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey avrPower = LogicalKeyboardKey(0x00100000d09);

  /// Represents the logical "Channel Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey channelDown = LogicalKeyboardKey(0x00100000d0a);

  /// Represents the logical "Channel Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey channelUp = LogicalKeyboardKey(0x00100000d0b);

  /// Represents the logical "Color F0 Red" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF0Red = LogicalKeyboardKey(0x00100000d0c);

  /// Represents the logical "Color F1 Green" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF1Green = LogicalKeyboardKey(0x00100000d0d);

  /// Represents the logical "Color F2 Yellow" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF2Yellow = LogicalKeyboardKey(0x00100000d0e);

  /// Represents the logical "Color F3 Blue" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF3Blue = LogicalKeyboardKey(0x00100000d0f);

  /// Represents the logical "Color F4 Grey" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF4Grey = LogicalKeyboardKey(0x00100000d10);

  /// Represents the logical "Color F5 Brown" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey colorF5Brown = LogicalKeyboardKey(0x00100000d11);

  /// Represents the logical "Closed Caption Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey closedCaptionToggle = LogicalKeyboardKey(0x00100000d12);

  /// Represents the logical "Dimmer" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey dimmer = LogicalKeyboardKey(0x00100000d13);

  /// Represents the logical "Display Swap" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey displaySwap = LogicalKeyboardKey(0x00100000d14);

  /// Represents the logical "Exit" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey exit = LogicalKeyboardKey(0x00100000d15);

  /// Represents the logical "Favorite Clear 0" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteClear0 = LogicalKeyboardKey(0x00100000d16);

  /// Represents the logical "Favorite Clear 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteClear1 = LogicalKeyboardKey(0x00100000d17);

  /// Represents the logical "Favorite Clear 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteClear2 = LogicalKeyboardKey(0x00100000d18);

  /// Represents the logical "Favorite Clear 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteClear3 = LogicalKeyboardKey(0x00100000d19);

  /// Represents the logical "Favorite Recall 0" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteRecall0 = LogicalKeyboardKey(0x00100000d1a);

  /// Represents the logical "Favorite Recall 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteRecall1 = LogicalKeyboardKey(0x00100000d1b);

  /// Represents the logical "Favorite Recall 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteRecall2 = LogicalKeyboardKey(0x00100000d1c);

  /// Represents the logical "Favorite Recall 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteRecall3 = LogicalKeyboardKey(0x00100000d1d);

  /// Represents the logical "Favorite Store 0" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteStore0 = LogicalKeyboardKey(0x00100000d1e);

  /// Represents the logical "Favorite Store 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteStore1 = LogicalKeyboardKey(0x00100000d1f);

  /// Represents the logical "Favorite Store 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteStore2 = LogicalKeyboardKey(0x00100000d20);

  /// Represents the logical "Favorite Store 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey favoriteStore3 = LogicalKeyboardKey(0x00100000d21);

  /// Represents the logical "Guide" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey guide = LogicalKeyboardKey(0x00100000d22);

  /// Represents the logical "Guide Next Day" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey guideNextDay = LogicalKeyboardKey(0x00100000d23);

  /// Represents the logical "Guide Previous Day" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey guidePreviousDay = LogicalKeyboardKey(0x00100000d24);

  /// Represents the logical "Info" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey info = LogicalKeyboardKey(0x00100000d25);

  /// Represents the logical "Instant Replay" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey instantReplay = LogicalKeyboardKey(0x00100000d26);

  /// Represents the logical "Link" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey link = LogicalKeyboardKey(0x00100000d27);

  /// Represents the logical "List Program" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey listProgram = LogicalKeyboardKey(0x00100000d28);

  /// Represents the logical "Live Content" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey liveContent = LogicalKeyboardKey(0x00100000d29);

  /// Represents the logical "Lock" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lock = LogicalKeyboardKey(0x00100000d2a);

  /// Represents the logical "Media Apps" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaApps = LogicalKeyboardKey(0x00100000d2b);

  /// Represents the logical "Media Fast Forward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaFastForward = LogicalKeyboardKey(0x00100000d2c);

  /// Represents the logical "Media Last" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaLast = LogicalKeyboardKey(0x00100000d2d);

  /// Represents the logical "Media Pause" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaPause = LogicalKeyboardKey(0x00100000d2e);

  /// Represents the logical "Media Play" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaPlay = LogicalKeyboardKey(0x00100000d2f);

  /// Represents the logical "Media Record" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaRecord = LogicalKeyboardKey(0x00100000d30);

  /// Represents the logical "Media Rewind" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaRewind = LogicalKeyboardKey(0x00100000d31);

  /// Represents the logical "Media Skip" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaSkip = LogicalKeyboardKey(0x00100000d32);

  /// Represents the logical "Next Favorite Channel" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey nextFavoriteChannel = LogicalKeyboardKey(0x00100000d33);

  /// Represents the logical "Next User Profile" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey nextUserProfile = LogicalKeyboardKey(0x00100000d34);

  /// Represents the logical "On Demand" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey onDemand = LogicalKeyboardKey(0x00100000d35);

  /// Represents the logical "P In P Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pInPDown = LogicalKeyboardKey(0x00100000d36);

  /// Represents the logical "P In P Move" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pInPMove = LogicalKeyboardKey(0x00100000d37);

  /// Represents the logical "P In P Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pInPToggle = LogicalKeyboardKey(0x00100000d38);

  /// Represents the logical "P In P Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pInPUp = LogicalKeyboardKey(0x00100000d39);

  /// Represents the logical "Play Speed Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey playSpeedDown = LogicalKeyboardKey(0x00100000d3a);

  /// Represents the logical "Play Speed Reset" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey playSpeedReset = LogicalKeyboardKey(0x00100000d3b);

  /// Represents the logical "Play Speed Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey playSpeedUp = LogicalKeyboardKey(0x00100000d3c);

  /// Represents the logical "Random Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey randomToggle = LogicalKeyboardKey(0x00100000d3d);

  /// Represents the logical "Rc Low Battery" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey rcLowBattery = LogicalKeyboardKey(0x00100000d3e);

  /// Represents the logical "Record Speed Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey recordSpeedNext = LogicalKeyboardKey(0x00100000d3f);

  /// Represents the logical "Rf Bypass" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey rfBypass = LogicalKeyboardKey(0x00100000d40);

  /// Represents the logical "Scan Channels Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey scanChannelsToggle = LogicalKeyboardKey(0x00100000d41);

  /// Represents the logical "Screen Mode Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey screenModeNext = LogicalKeyboardKey(0x00100000d42);

  /// Represents the logical "Settings" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey settings = LogicalKeyboardKey(0x00100000d43);

  /// Represents the logical "Split Screen Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey splitScreenToggle = LogicalKeyboardKey(0x00100000d44);

  /// Represents the logical "STB Input" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey stbInput = LogicalKeyboardKey(0x00100000d45);

  /// Represents the logical "STB Power" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey stbPower = LogicalKeyboardKey(0x00100000d46);

  /// Represents the logical "Subtitle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey subtitle = LogicalKeyboardKey(0x00100000d47);

  /// Represents the logical "Teletext" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey teletext = LogicalKeyboardKey(0x00100000d48);

  /// Represents the logical "TV" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tv = LogicalKeyboardKey(0x00100000d49);

  /// Represents the logical "TV Input" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInput = LogicalKeyboardKey(0x00100000d4a);

  /// Represents the logical "TV Power" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvPower = LogicalKeyboardKey(0x00100000d4b);

  /// Represents the logical "Video Mode Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey videoModeNext = LogicalKeyboardKey(0x00100000d4c);

  /// Represents the logical "Wink" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey wink = LogicalKeyboardKey(0x00100000d4d);

  /// Represents the logical "Zoom Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey zoomToggle = LogicalKeyboardKey(0x00100000d4e);

  /// Represents the logical "DVR" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey dvr = LogicalKeyboardKey(0x00100000d4f);

  /// Represents the logical "Media Audio Track" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaAudioTrack = LogicalKeyboardKey(0x00100000d50);

  /// Represents the logical "Media Skip Backward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaSkipBackward = LogicalKeyboardKey(0x00100000d51);

  /// Represents the logical "Media Skip Forward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaSkipForward = LogicalKeyboardKey(0x00100000d52);

  /// Represents the logical "Media Step Backward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaStepBackward = LogicalKeyboardKey(0x00100000d53);

  /// Represents the logical "Media Step Forward" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaStepForward = LogicalKeyboardKey(0x00100000d54);

  /// Represents the logical "Media Top Menu" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaTopMenu = LogicalKeyboardKey(0x00100000d55);

  /// Represents the logical "Navigate In" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey navigateIn = LogicalKeyboardKey(0x00100000d56);

  /// Represents the logical "Navigate Next" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey navigateNext = LogicalKeyboardKey(0x00100000d57);

  /// Represents the logical "Navigate Out" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey navigateOut = LogicalKeyboardKey(0x00100000d58);

  /// Represents the logical "Navigate Previous" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey navigatePrevious = LogicalKeyboardKey(0x00100000d59);

  /// Represents the logical "Pairing" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey pairing = LogicalKeyboardKey(0x00100000d5a);

  /// Represents the logical "Media Close" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mediaClose = LogicalKeyboardKey(0x00100000d5b);

  /// Represents the logical "Audio Bass Boost Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioBassBoostToggle = LogicalKeyboardKey(0x00100000e02);

  /// Represents the logical "Audio Treble Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioTrebleDown = LogicalKeyboardKey(0x00100000e04);

  /// Represents the logical "Audio Treble Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey audioTrebleUp = LogicalKeyboardKey(0x00100000e05);

  /// Represents the logical "Microphone Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey microphoneToggle = LogicalKeyboardKey(0x00100000e06);

  /// Represents the logical "Microphone Volume Down" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey microphoneVolumeDown = LogicalKeyboardKey(0x00100000e07);

  /// Represents the logical "Microphone Volume Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey microphoneVolumeUp = LogicalKeyboardKey(0x00100000e08);

  /// Represents the logical "Microphone Volume Mute" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey microphoneVolumeMute = LogicalKeyboardKey(0x00100000e09);

  /// Represents the logical "Speech Correction List" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey speechCorrectionList = LogicalKeyboardKey(0x00100000f01);

  /// Represents the logical "Speech Input Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey speechInputToggle = LogicalKeyboardKey(0x00100000f02);

  /// Represents the logical "App Switch" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey appSwitch = LogicalKeyboardKey(0x00100001001);

  /// Represents the logical "Call" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey call = LogicalKeyboardKey(0x00100001002);

  /// Represents the logical "Camera Focus" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey cameraFocus = LogicalKeyboardKey(0x00100001003);

  /// Represents the logical "End Call" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey endCall = LogicalKeyboardKey(0x00100001004);

  /// Represents the logical "Go Back" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey goBack = LogicalKeyboardKey(0x00100001005);

  /// Represents the logical "Go Home" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey goHome = LogicalKeyboardKey(0x00100001006);

  /// Represents the logical "Headset Hook" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey headsetHook = LogicalKeyboardKey(0x00100001007);

  /// Represents the logical "Last Number Redial" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lastNumberRedial = LogicalKeyboardKey(0x00100001008);

  /// Represents the logical "Notification" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey notification = LogicalKeyboardKey(0x00100001009);

  /// Represents the logical "Manner Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey mannerMode = LogicalKeyboardKey(0x0010000100a);

  /// Represents the logical "Voice Dial" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey voiceDial = LogicalKeyboardKey(0x0010000100b);

  /// Represents the logical "TV 3 D Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tv3DMode = LogicalKeyboardKey(0x00100001101);

  /// Represents the logical "TV Antenna Cable" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvAntennaCable = LogicalKeyboardKey(0x00100001102);

  /// Represents the logical "TV Audio Description" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvAudioDescription = LogicalKeyboardKey(0x00100001103);

  /// Represents the logical "TV Audio Description Mix Down" key on the
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvAudioDescriptionMixDown = LogicalKeyboardKey(0x00100001104);

  /// Represents the logical "TV Audio Description Mix Up" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvAudioDescriptionMixUp = LogicalKeyboardKey(0x00100001105);

  /// Represents the logical "TV Contents Menu" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvContentsMenu = LogicalKeyboardKey(0x00100001106);

  /// Represents the logical "TV Data Service" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvDataService = LogicalKeyboardKey(0x00100001107);

  /// Represents the logical "TV Input Component 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputComponent1 = LogicalKeyboardKey(0x00100001108);

  /// Represents the logical "TV Input Component 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputComponent2 = LogicalKeyboardKey(0x00100001109);

  /// Represents the logical "TV Input Composite 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputComposite1 = LogicalKeyboardKey(0x0010000110a);

  /// Represents the logical "TV Input Composite 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputComposite2 = LogicalKeyboardKey(0x0010000110b);

  /// Represents the logical "TV Input HDMI 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputHDMI1 = LogicalKeyboardKey(0x0010000110c);

  /// Represents the logical "TV Input HDMI 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputHDMI2 = LogicalKeyboardKey(0x0010000110d);

  /// Represents the logical "TV Input HDMI 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputHDMI3 = LogicalKeyboardKey(0x0010000110e);

  /// Represents the logical "TV Input HDMI 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputHDMI4 = LogicalKeyboardKey(0x0010000110f);

  /// Represents the logical "TV Input VGA 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvInputVGA1 = LogicalKeyboardKey(0x00100001110);

  /// Represents the logical "TV Media Context" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvMediaContext = LogicalKeyboardKey(0x00100001111);

  /// Represents the logical "TV Network" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvNetwork = LogicalKeyboardKey(0x00100001112);

  /// Represents the logical "TV Number Entry" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvNumberEntry = LogicalKeyboardKey(0x00100001113);

  /// Represents the logical "TV Radio Service" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvRadioService = LogicalKeyboardKey(0x00100001114);

  /// Represents the logical "TV Satellite" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvSatellite = LogicalKeyboardKey(0x00100001115);

  /// Represents the logical "TV Satellite BS" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvSatelliteBS = LogicalKeyboardKey(0x00100001116);

  /// Represents the logical "TV Satellite CS" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvSatelliteCS = LogicalKeyboardKey(0x00100001117);

  /// Represents the logical "TV Satellite Toggle" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvSatelliteToggle = LogicalKeyboardKey(0x00100001118);

  /// Represents the logical "TV Terrestrial Analog" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvTerrestrialAnalog = LogicalKeyboardKey(0x00100001119);

  /// Represents the logical "TV Terrestrial Digital" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvTerrestrialDigital = LogicalKeyboardKey(0x0010000111a);

  /// Represents the logical "TV Timer" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey tvTimer = LogicalKeyboardKey(0x0010000111b);

  /// Represents the logical "Key 11" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey key11 = LogicalKeyboardKey(0x00100001201);

  /// Represents the logical "Key 12" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey key12 = LogicalKeyboardKey(0x00100001202);

  /// Represents the logical "Suspend" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey suspend = LogicalKeyboardKey(0x00200000000);

  /// Represents the logical "Resume" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey resume = LogicalKeyboardKey(0x00200000001);

  /// Represents the logical "Sleep" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey sleep = LogicalKeyboardKey(0x00200000002);

  /// Represents the logical "Abort" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey abort = LogicalKeyboardKey(0x00200000003);

  /// Represents the logical "Lang 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lang1 = LogicalKeyboardKey(0x00200000010);

  /// Represents the logical "Lang 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lang2 = LogicalKeyboardKey(0x00200000011);

  /// Represents the logical "Lang 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lang3 = LogicalKeyboardKey(0x00200000012);

  /// Represents the logical "Lang 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lang4 = LogicalKeyboardKey(0x00200000013);

  /// Represents the logical "Lang 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey lang5 = LogicalKeyboardKey(0x00200000014);

  /// Represents the logical "Intl Backslash" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey intlBackslash = LogicalKeyboardKey(0x00200000020);

  /// Represents the logical "Intl Ro" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey intlRo = LogicalKeyboardKey(0x00200000021);

  /// Represents the logical "Intl Yen" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey intlYen = LogicalKeyboardKey(0x00200000022);

  /// Represents the logical "Control Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey controlLeft = LogicalKeyboardKey(0x00200000100);

  /// Represents the logical "Control Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey controlRight = LogicalKeyboardKey(0x00200000101);

  /// Represents the logical "Shift Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey shiftLeft = LogicalKeyboardKey(0x00200000102);

  /// Represents the logical "Shift Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey shiftRight = LogicalKeyboardKey(0x00200000103);

  /// Represents the logical "Alt Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey altLeft = LogicalKeyboardKey(0x00200000104);

  /// Represents the logical "Alt Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey altRight = LogicalKeyboardKey(0x00200000105);

  /// Represents the logical "Meta Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey metaLeft = LogicalKeyboardKey(0x00200000106);

  /// Represents the logical "Meta Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey metaRight = LogicalKeyboardKey(0x00200000107);

  /// Represents the logical "Control" key on the keyboard.
  ///
  /// This key represents the union of the keys {controlLeft, controlRight} when
  /// comparing keys. This key will never be generated directly, its main use is
  /// in defining key maps.
  static const LogicalKeyboardKey control = LogicalKeyboardKey(0x002000001f0);

  /// Represents the logical "Shift" key on the keyboard.
  ///
  /// This key represents the union of the keys {shiftLeft, shiftRight} when
  /// comparing keys. This key will never be generated directly, its main use is
  /// in defining key maps.
  static const LogicalKeyboardKey shift = LogicalKeyboardKey(0x002000001f2);

  /// Represents the logical "Alt" key on the keyboard.
  ///
  /// This key represents the union of the keys {altLeft, altRight} when
  /// comparing keys. This key will never be generated directly, its main use is
  /// in defining key maps.
  static const LogicalKeyboardKey alt = LogicalKeyboardKey(0x002000001f4);

  /// Represents the logical "Meta" key on the keyboard.
  ///
  /// This key represents the union of the keys {metaLeft, metaRight} when
  /// comparing keys. This key will never be generated directly, its main use is
  /// in defining key maps.
  static const LogicalKeyboardKey meta = LogicalKeyboardKey(0x002000001f6);

  /// Represents the logical "Numpad Enter" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadEnter = LogicalKeyboardKey(0x0020000020d);

  /// Represents the logical "Numpad Paren Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadParenLeft = LogicalKeyboardKey(0x00200000228);

  /// Represents the logical "Numpad Paren Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadParenRight = LogicalKeyboardKey(0x00200000229);

  /// Represents the logical "Numpad Multiply" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadMultiply = LogicalKeyboardKey(0x0020000022a);

  /// Represents the logical "Numpad Add" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadAdd = LogicalKeyboardKey(0x0020000022b);

  /// Represents the logical "Numpad Comma" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadComma = LogicalKeyboardKey(0x0020000022c);

  /// Represents the logical "Numpad Subtract" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadSubtract = LogicalKeyboardKey(0x0020000022d);

  /// Represents the logical "Numpad Decimal" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadDecimal = LogicalKeyboardKey(0x0020000022e);

  /// Represents the logical "Numpad Divide" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadDivide = LogicalKeyboardKey(0x0020000022f);

  /// Represents the logical "Numpad 0" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad0 = LogicalKeyboardKey(0x00200000230);

  /// Represents the logical "Numpad 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad1 = LogicalKeyboardKey(0x00200000231);

  /// Represents the logical "Numpad 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad2 = LogicalKeyboardKey(0x00200000232);

  /// Represents the logical "Numpad 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad3 = LogicalKeyboardKey(0x00200000233);

  /// Represents the logical "Numpad 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad4 = LogicalKeyboardKey(0x00200000234);

  /// Represents the logical "Numpad 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad5 = LogicalKeyboardKey(0x00200000235);

  /// Represents the logical "Numpad 6" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad6 = LogicalKeyboardKey(0x00200000236);

  /// Represents the logical "Numpad 7" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad7 = LogicalKeyboardKey(0x00200000237);

  /// Represents the logical "Numpad 8" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad8 = LogicalKeyboardKey(0x00200000238);

  /// Represents the logical "Numpad 9" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpad9 = LogicalKeyboardKey(0x00200000239);

  /// Represents the logical "Numpad Equal" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey numpadEqual = LogicalKeyboardKey(0x0020000023d);

  /// Represents the logical "Game Button 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton1 = LogicalKeyboardKey(0x00200000301);

  /// Represents the logical "Game Button 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton2 = LogicalKeyboardKey(0x00200000302);

  /// Represents the logical "Game Button 3" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton3 = LogicalKeyboardKey(0x00200000303);

  /// Represents the logical "Game Button 4" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton4 = LogicalKeyboardKey(0x00200000304);

  /// Represents the logical "Game Button 5" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton5 = LogicalKeyboardKey(0x00200000305);

  /// Represents the logical "Game Button 6" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton6 = LogicalKeyboardKey(0x00200000306);

  /// Represents the logical "Game Button 7" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton7 = LogicalKeyboardKey(0x00200000307);

  /// Represents the logical "Game Button 8" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton8 = LogicalKeyboardKey(0x00200000308);

  /// Represents the logical "Game Button 9" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton9 = LogicalKeyboardKey(0x00200000309);

  /// Represents the logical "Game Button 10" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton10 = LogicalKeyboardKey(0x0020000030a);

  /// Represents the logical "Game Button 11" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton11 = LogicalKeyboardKey(0x0020000030b);

  /// Represents the logical "Game Button 12" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton12 = LogicalKeyboardKey(0x0020000030c);

  /// Represents the logical "Game Button 13" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton13 = LogicalKeyboardKey(0x0020000030d);

  /// Represents the logical "Game Button 14" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton14 = LogicalKeyboardKey(0x0020000030e);

  /// Represents the logical "Game Button 15" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton15 = LogicalKeyboardKey(0x0020000030f);

  /// Represents the logical "Game Button 16" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButton16 = LogicalKeyboardKey(0x00200000310);

  /// Represents the logical "Game Button A" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonA = LogicalKeyboardKey(0x00200000311);

  /// Represents the logical "Game Button B" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonB = LogicalKeyboardKey(0x00200000312);

  /// Represents the logical "Game Button C" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonC = LogicalKeyboardKey(0x00200000313);

  /// Represents the logical "Game Button Left 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonLeft1 = LogicalKeyboardKey(0x00200000314);

  /// Represents the logical "Game Button Left 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonLeft2 = LogicalKeyboardKey(0x00200000315);

  /// Represents the logical "Game Button Mode" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonMode = LogicalKeyboardKey(0x00200000316);

  /// Represents the logical "Game Button Right 1" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonRight1 = LogicalKeyboardKey(0x00200000317);

  /// Represents the logical "Game Button Right 2" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonRight2 = LogicalKeyboardKey(0x00200000318);

  /// Represents the logical "Game Button Select" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonSelect = LogicalKeyboardKey(0x00200000319);

  /// Represents the logical "Game Button Start" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonStart = LogicalKeyboardKey(0x0020000031a);

  /// Represents the logical "Game Button Thumb Left" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonThumbLeft = LogicalKeyboardKey(0x0020000031b);

  /// Represents the logical "Game Button Thumb Right" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonThumbRight = LogicalKeyboardKey(0x0020000031c);

  /// Represents the logical "Game Button X" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonX = LogicalKeyboardKey(0x0020000031d);

  /// Represents the logical "Game Button Y" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonY = LogicalKeyboardKey(0x0020000031e);

  /// Represents the logical "Game Button Z" key on the keyboard.
  ///
  /// See the function [RawKeyEvent.logicalKey] for more information.
  static const LogicalKeyboardKey gameButtonZ = LogicalKeyboardKey(0x0020000031f);

  /// A list of all predefined constant [LogicalKeyboardKey]s.
  static Iterable<LogicalKeyboardKey> get knownLogicalKeys => _knownLogicalKeys.values;

  // A list of all predefined constant LogicalKeyboardKeys so they can be
  // searched.
  static const Map<int, LogicalKeyboardKey> _knownLogicalKeys = <int, LogicalKeyboardKey>{
    0x00000000020: space,
    0x00000000021: exclamation,
    0x00000000022: quote,
    0x00000000023: numberSign,
    0x00000000024: dollar,
    0x00000000025: percent,
    0x00000000026: ampersand,
    0x00000000027: quoteSingle,
    0x00000000028: parenthesisLeft,
    0x00000000029: parenthesisRight,
    0x0000000002a: asterisk,
    0x0000000002b: add,
    0x0000000002c: comma,
    0x0000000002d: minus,
    0x0000000002e: period,
    0x0000000002f: slash,
    0x00000000030: digit0,
    0x00000000031: digit1,
    0x00000000032: digit2,
    0x00000000033: digit3,
    0x00000000034: digit4,
    0x00000000035: digit5,
    0x00000000036: digit6,
    0x00000000037: digit7,
    0x00000000038: digit8,
    0x00000000039: digit9,
    0x0000000003a: colon,
    0x0000000003b: semicolon,
    0x0000000003c: less,
    0x0000000003d: equal,
    0x0000000003e: greater,
    0x0000000003f: question,
    0x00000000040: at,
    0x0000000005b: bracketLeft,
    0x0000000005c: backslash,
    0x0000000005d: bracketRight,
    0x0000000005e: caret,
    0x0000000005f: underscore,
    0x00000000060: backquote,
    0x00000000061: keyA,
    0x00000000062: keyB,
    0x00000000063: keyC,
    0x00000000064: keyD,
    0x00000000065: keyE,
    0x00000000066: keyF,
    0x00000000067: keyG,
    0x00000000068: keyH,
    0x00000000069: keyI,
    0x0000000006a: keyJ,
    0x0000000006b: keyK,
    0x0000000006c: keyL,
    0x0000000006d: keyM,
    0x0000000006e: keyN,
    0x0000000006f: keyO,
    0x00000000070: keyP,
    0x00000000071: keyQ,
    0x00000000072: keyR,
    0x00000000073: keyS,
    0x00000000074: keyT,
    0x00000000075: keyU,
    0x00000000076: keyV,
    0x00000000077: keyW,
    0x00000000078: keyX,
    0x00000000079: keyY,
    0x0000000007a: keyZ,
    0x0000000007b: braceLeft,
    0x0000000007c: bar,
    0x0000000007d: braceRight,
    0x0000000007e: tilde,
    0x00100000001: unidentified,
    0x00100000008: backspace,
    0x00100000009: tab,
    0x0010000000d: enter,
    0x0010000001b: escape,
    0x0010000007f: delete,
    0x00100000101: accel,
    0x00100000103: altGraph,
    0x00100000104: capsLock,
    0x00100000106: fn,
    0x00100000107: fnLock,
    0x00100000108: hyper,
    0x0010000010a: numLock,
    0x0010000010c: scrollLock,
    0x0010000010e: superKey,
    0x0010000010f: symbol,
    0x00100000110: symbolLock,
    0x00100000111: shiftLevel5,
    0x00100000301: arrowDown,
    0x00100000302: arrowLeft,
    0x00100000303: arrowRight,
    0x00100000304: arrowUp,
    0x00100000305: end,
    0x00100000306: home,
    0x00100000307: pageDown,
    0x00100000308: pageUp,
    0x00100000401: clear,
    0x00100000402: copy,
    0x00100000403: crSel,
    0x00100000404: cut,
    0x00100000405: eraseEof,
    0x00100000406: exSel,
    0x00100000407: insert,
    0x00100000408: paste,
    0x00100000409: redo,
    0x0010000040a: undo,
    0x00100000501: accept,
    0x00100000502: again,
    0x00100000503: attn,
    0x00100000504: cancel,
    0x00100000505: contextMenu,
    0x00100000506: execute,
    0x00100000507: find,
    0x00100000508: help,
    0x00100000509: pause,
    0x0010000050a: play,
    0x0010000050b: props,
    0x0010000050c: select,
    0x0010000050d: zoomIn,
    0x0010000050e: zoomOut,
    0x00100000601: brightnessDown,
    0x00100000602: brightnessUp,
    0x00100000603: camera,
    0x00100000604: eject,
    0x00100000605: logOff,
    0x00100000606: power,
    0x00100000607: powerOff,
    0x00100000608: printScreen,
    0x00100000609: hibernate,
    0x0010000060a: standby,
    0x0010000060b: wakeUp,
    0x00100000701: allCandidates,
    0x00100000702: alphanumeric,
    0x00100000703: codeInput,
    0x00100000704: compose,
    0x00100000705: convert,
    0x00100000706: finalMode,
    0x00100000707: groupFirst,
    0x00100000708: groupLast,
    0x00100000709: groupNext,
    0x0010000070a: groupPrevious,
    0x0010000070b: modeChange,
    0x0010000070c: nextCandidate,
    0x0010000070d: nonConvert,
    0x0010000070e: previousCandidate,
    0x0010000070f: process,
    0x00100000710: singleCandidate,
    0x00100000711: hangulMode,
    0x00100000712: hanjaMode,
    0x00100000713: junjaMode,
    0x00100000714: eisu,
    0x00100000715: hankaku,
    0x00100000716: hiragana,
    0x00100000717: hiraganaKatakana,
    0x00100000718: kanaMode,
    0x00100000719: kanjiMode,
    0x0010000071a: katakana,
    0x0010000071b: romaji,
    0x0010000071c: zenkaku,
    0x0010000071d: zenkakuHankaku,
    0x00100000801: f1,
    0x00100000802: f2,
    0x00100000803: f3,
    0x00100000804: f4,
    0x00100000805: f5,
    0x00100000806: f6,
    0x00100000807: f7,
    0x00100000808: f8,
    0x00100000809: f9,
    0x0010000080a: f10,
    0x0010000080b: f11,
    0x0010000080c: f12,
    0x0010000080d: f13,
    0x0010000080e: f14,
    0x0010000080f: f15,
    0x00100000810: f16,
    0x00100000811: f17,
    0x00100000812: f18,
    0x00100000813: f19,
    0x00100000814: f20,
    0x00100000815: f21,
    0x00100000816: f22,
    0x00100000817: f23,
    0x00100000818: f24,
    0x00100000901: soft1,
    0x00100000902: soft2,
    0x00100000903: soft3,
    0x00100000904: soft4,
    0x00100000905: soft5,
    0x00100000906: soft6,
    0x00100000907: soft7,
    0x00100000908: soft8,
    0x00100000a01: close,
    0x00100000a02: mailForward,
    0x00100000a03: mailReply,
    0x00100000a04: mailSend,
    0x00100000a05: mediaPlayPause,
    0x00100000a07: mediaStop,
    0x00100000a08: mediaTrackNext,
    0x00100000a09: mediaTrackPrevious,
    0x00100000a0a: newKey,
    0x00100000a0b: open,
    0x00100000a0c: print,
    0x00100000a0d: save,
    0x00100000a0e: spellCheck,
    0x00100000a0f: audioVolumeDown,
    0x00100000a10: audioVolumeUp,
    0x00100000a11: audioVolumeMute,
    0x00100000b01: launchApplication2,
    0x00100000b02: launchCalendar,
    0x00100000b03: launchMail,
    0x00100000b04: launchMediaPlayer,
    0x00100000b05: launchMusicPlayer,
    0x00100000b06: launchApplication1,
    0x00100000b07: launchScreenSaver,
    0x00100000b08: launchSpreadsheet,
    0x00100000b09: launchWebBrowser,
    0x00100000b0a: launchWebCam,
    0x00100000b0b: launchWordProcessor,
    0x00100000b0c: launchContacts,
    0x00100000b0d: launchPhone,
    0x00100000b0e: launchAssistant,
    0x00100000b0f: launchControlPanel,
    0x00100000c01: browserBack,
    0x00100000c02: browserFavorites,
    0x00100000c03: browserForward,
    0x00100000c04: browserHome,
    0x00100000c05: browserRefresh,
    0x00100000c06: browserSearch,
    0x00100000c07: browserStop,
    0x00100000d01: audioBalanceLeft,
    0x00100000d02: audioBalanceRight,
    0x00100000d03: audioBassBoostDown,
    0x00100000d04: audioBassBoostUp,
    0x00100000d05: audioFaderFront,
    0x00100000d06: audioFaderRear,
    0x00100000d07: audioSurroundModeNext,
    0x00100000d08: avrInput,
    0x00100000d09: avrPower,
    0x00100000d0a: channelDown,
    0x00100000d0b: channelUp,
    0x00100000d0c: colorF0Red,
    0x00100000d0d: colorF1Green,
    0x00100000d0e: colorF2Yellow,
    0x00100000d0f: colorF3Blue,
    0x00100000d10: colorF4Grey,
    0x00100000d11: colorF5Brown,
    0x00100000d12: closedCaptionToggle,
    0x00100000d13: dimmer,
    0x00100000d14: displaySwap,
    0x00100000d15: exit,
    0x00100000d16: favoriteClear0,
    0x00100000d17: favoriteClear1,
    0x00100000d18: favoriteClear2,
    0x00100000d19: favoriteClear3,
    0x00100000d1a: favoriteRecall0,
    0x00100000d1b: favoriteRecall1,
    0x00100000d1c: favoriteRecall2,
    0x00100000d1d: favoriteRecall3,
    0x00100000d1e: favoriteStore0,
    0x00100000d1f: favoriteStore1,
    0x00100000d20: favoriteStore2,
    0x00100000d21: favoriteStore3,
    0x00100000d22: guide,
    0x00100000d23: guideNextDay,
    0x00100000d24: guidePreviousDay,
    0x00100000d25: info,
    0x00100000d26: instantReplay,
    0x00100000d27: link,
    0x00100000d28: listProgram,
    0x00100000d29: liveContent,
    0x00100000d2a: lock,
    0x00100000d2b: mediaApps,
    0x00100000d2c: mediaFastForward,
    0x00100000d2d: mediaLast,
    0x00100000d2e: mediaPause,
    0x00100000d2f: mediaPlay,
    0x00100000d30: mediaRecord,
    0x00100000d31: mediaRewind,
    0x00100000d32: mediaSkip,
    0x00100000d33: nextFavoriteChannel,
    0x00100000d34: nextUserProfile,
    0x00100000d35: onDemand,
    0x00100000d36: pInPDown,
    0x00100000d37: pInPMove,
    0x00100000d38: pInPToggle,
    0x00100000d39: pInPUp,
    0x00100000d3a: playSpeedDown,
    0x00100000d3b: playSpeedReset,
    0x00100000d3c: playSpeedUp,
    0x00100000d3d: randomToggle,
    0x00100000d3e: rcLowBattery,
    0x00100000d3f: recordSpeedNext,
    0x00100000d40: rfBypass,
    0x00100000d41: scanChannelsToggle,
    0x00100000d42: screenModeNext,
    0x00100000d43: settings,
    0x00100000d44: splitScreenToggle,
    0x00100000d45: stbInput,
    0x00100000d46: stbPower,
    0x00100000d47: subtitle,
    0x00100000d48: teletext,
    0x00100000d49: tv,
    0x00100000d4a: tvInput,
    0x00100000d4b: tvPower,
    0x00100000d4c: videoModeNext,
    0x00100000d4d: wink,
    0x00100000d4e: zoomToggle,
    0x00100000d4f: dvr,
    0x00100000d50: mediaAudioTrack,
    0x00100000d51: mediaSkipBackward,
    0x00100000d52: mediaSkipForward,
    0x00100000d53: mediaStepBackward,
    0x00100000d54: mediaStepForward,
    0x00100000d55: mediaTopMenu,
    0x00100000d56: navigateIn,
    0x00100000d57: navigateNext,
    0x00100000d58: navigateOut,
    0x00100000d59: navigatePrevious,
    0x00100000d5a: pairing,
    0x00100000d5b: mediaClose,
    0x00100000e02: audioBassBoostToggle,
    0x00100000e04: audioTrebleDown,
    0x00100000e05: audioTrebleUp,
    0x00100000e06: microphoneToggle,
    0x00100000e07: microphoneVolumeDown,
    0x00100000e08: microphoneVolumeUp,
    0x00100000e09: microphoneVolumeMute,
    0x00100000f01: speechCorrectionList,
    0x00100000f02: speechInputToggle,
    0x00100001001: appSwitch,
    0x00100001002: call,
    0x00100001003: cameraFocus,
    0x00100001004: endCall,
    0x00100001005: goBack,
    0x00100001006: goHome,
    0x00100001007: headsetHook,
    0x00100001008: lastNumberRedial,
    0x00100001009: notification,
    0x0010000100a: mannerMode,
    0x0010000100b: voiceDial,
    0x00100001101: tv3DMode,
    0x00100001102: tvAntennaCable,
    0x00100001103: tvAudioDescription,
    0x00100001104: tvAudioDescriptionMixDown,
    0x00100001105: tvAudioDescriptionMixUp,
    0x00100001106: tvContentsMenu,
    0x00100001107: tvDataService,
    0x00100001108: tvInputComponent1,
    0x00100001109: tvInputComponent2,
    0x0010000110a: tvInputComposite1,
    0x0010000110b: tvInputComposite2,
    0x0010000110c: tvInputHDMI1,
    0x0010000110d: tvInputHDMI2,
    0x0010000110e: tvInputHDMI3,
    0x0010000110f: tvInputHDMI4,
    0x00100001110: tvInputVGA1,
    0x00100001111: tvMediaContext,
    0x00100001112: tvNetwork,
    0x00100001113: tvNumberEntry,
    0x00100001114: tvRadioService,
    0x00100001115: tvSatellite,
    0x00100001116: tvSatelliteBS,
    0x00100001117: tvSatelliteCS,
    0x00100001118: tvSatelliteToggle,
    0x00100001119: tvTerrestrialAnalog,
    0x0010000111a: tvTerrestrialDigital,
    0x0010000111b: tvTimer,
    0x00100001201: key11,
    0x00100001202: key12,
    0x00200000000: suspend,
    0x00200000001: resume,
    0x00200000002: sleep,
    0x00200000003: abort,
    0x00200000010: lang1,
    0x00200000011: lang2,
    0x00200000012: lang3,
    0x00200000013: lang4,
    0x00200000014: lang5,
    0x00200000020: intlBackslash,
    0x00200000021: intlRo,
    0x00200000022: intlYen,
    0x00200000100: controlLeft,
    0x00200000101: controlRight,
    0x00200000102: shiftLeft,
    0x00200000103: shiftRight,
    0x00200000104: altLeft,
    0x00200000105: altRight,
    0x00200000106: metaLeft,
    0x00200000107: metaRight,
    0x002000001f0: control,
    0x002000001f2: shift,
    0x002000001f4: alt,
    0x002000001f6: meta,
    0x0020000020d: numpadEnter,
    0x00200000228: numpadParenLeft,
    0x00200000229: numpadParenRight,
    0x0020000022a: numpadMultiply,
    0x0020000022b: numpadAdd,
    0x0020000022c: numpadComma,
    0x0020000022d: numpadSubtract,
    0x0020000022e: numpadDecimal,
    0x0020000022f: numpadDivide,
    0x00200000230: numpad0,
    0x00200000231: numpad1,
    0x00200000232: numpad2,
    0x00200000233: numpad3,
    0x00200000234: numpad4,
    0x00200000235: numpad5,
    0x00200000236: numpad6,
    0x00200000237: numpad7,
    0x00200000238: numpad8,
    0x00200000239: numpad9,
    0x0020000023d: numpadEqual,
    0x00200000301: gameButton1,
    0x00200000302: gameButton2,
    0x00200000303: gameButton3,
    0x00200000304: gameButton4,
    0x00200000305: gameButton5,
    0x00200000306: gameButton6,
    0x00200000307: gameButton7,
    0x00200000308: gameButton8,
    0x00200000309: gameButton9,
    0x0020000030a: gameButton10,
    0x0020000030b: gameButton11,
    0x0020000030c: gameButton12,
    0x0020000030d: gameButton13,
    0x0020000030e: gameButton14,
    0x0020000030f: gameButton15,
    0x00200000310: gameButton16,
    0x00200000311: gameButtonA,
    0x00200000312: gameButtonB,
    0x00200000313: gameButtonC,
    0x00200000314: gameButtonLeft1,
    0x00200000315: gameButtonLeft2,
    0x00200000316: gameButtonMode,
    0x00200000317: gameButtonRight1,
    0x00200000318: gameButtonRight2,
    0x00200000319: gameButtonSelect,
    0x0020000031a: gameButtonStart,
    0x0020000031b: gameButtonThumbLeft,
    0x0020000031c: gameButtonThumbRight,
    0x0020000031d: gameButtonX,
    0x0020000031e: gameButtonY,
    0x0020000031f: gameButtonZ,
  };

  // A map of keys to the pseudo-key synonym for that key.
  static final Map<LogicalKeyboardKey, Set<LogicalKeyboardKey>> _synonyms =
      <LogicalKeyboardKey, Set<LogicalKeyboardKey>>{
        shiftLeft: <LogicalKeyboardKey>{shift},
        shiftRight: <LogicalKeyboardKey>{shift},
        metaLeft: <LogicalKeyboardKey>{meta},
        metaRight: <LogicalKeyboardKey>{meta},
        altLeft: <LogicalKeyboardKey>{alt},
        altRight: <LogicalKeyboardKey>{alt},
        controlLeft: <LogicalKeyboardKey>{control},
        controlRight: <LogicalKeyboardKey>{control},
      };

  // A map of pseudo-key to the set of keys that are synonyms for that pseudo-key.
  static final Map<LogicalKeyboardKey, Set<LogicalKeyboardKey>> _reverseSynonyms =
      <LogicalKeyboardKey, Set<LogicalKeyboardKey>>{
        shift: <LogicalKeyboardKey>{shiftLeft, shiftRight},
        meta: <LogicalKeyboardKey>{metaLeft, metaRight},
        alt: <LogicalKeyboardKey>{altLeft, altRight},
        control: <LogicalKeyboardKey>{controlLeft, controlRight},
      };

  static const Map<int, String> _keyLabels = <int, String>{
    0x00000000020: 'Space',
    0x00000000021: 'Exclamation',
    0x00000000022: 'Quote',
    0x00000000023: 'Number Sign',
    0x00000000024: 'Dollar',
    0x00000000025: 'Percent',
    0x00000000026: 'Ampersand',
    0x00000000027: 'Quote Single',
    0x00000000028: 'Parenthesis Left',
    0x00000000029: 'Parenthesis Right',
    0x0000000002a: 'Asterisk',
    0x0000000002b: 'Add',
    0x0000000002c: 'Comma',
    0x0000000002d: 'Minus',
    0x0000000002e: 'Period',
    0x0000000002f: 'Slash',
    0x00000000030: 'Digit 0',
    0x00000000031: 'Digit 1',
    0x00000000032: 'Digit 2',
    0x00000000033: 'Digit 3',
    0x00000000034: 'Digit 4',
    0x00000000035: 'Digit 5',
    0x00000000036: 'Digit 6',
    0x00000000037: 'Digit 7',
    0x00000000038: 'Digit 8',
    0x00000000039: 'Digit 9',
    0x0000000003a: 'Colon',
    0x0000000003b: 'Semicolon',
    0x0000000003c: 'Less',
    0x0000000003d: 'Equal',
    0x0000000003e: 'Greater',
    0x0000000003f: 'Question',
    0x00000000040: 'At',
    0x0000000005b: 'Bracket Left',
    0x0000000005c: 'Backslash',
    0x0000000005d: 'Bracket Right',
    0x0000000005e: 'Caret',
    0x0000000005f: 'Underscore',
    0x00000000060: 'Backquote',
    0x00000000061: 'Key A',
    0x00000000062: 'Key B',
    0x00000000063: 'Key C',
    0x00000000064: 'Key D',
    0x00000000065: 'Key E',
    0x00000000066: 'Key F',
    0x00000000067: 'Key G',
    0x00000000068: 'Key H',
    0x00000000069: 'Key I',
    0x0000000006a: 'Key J',
    0x0000000006b: 'Key K',
    0x0000000006c: 'Key L',
    0x0000000006d: 'Key M',
    0x0000000006e: 'Key N',
    0x0000000006f: 'Key O',
    0x00000000070: 'Key P',
    0x00000000071: 'Key Q',
    0x00000000072: 'Key R',
    0x00000000073: 'Key S',
    0x00000000074: 'Key T',
    0x00000000075: 'Key U',
    0x00000000076: 'Key V',
    0x00000000077: 'Key W',
    0x00000000078: 'Key X',
    0x00000000079: 'Key Y',
    0x0000000007a: 'Key Z',
    0x0000000007b: 'Brace Left',
    0x0000000007c: 'Bar',
    0x0000000007d: 'Brace Right',
    0x0000000007e: 'Tilde',
    0x00100000001: 'Unidentified',
    0x00100000008: 'Backspace',
    0x00100000009: 'Tab',
    0x0010000000d: 'Enter',
    0x0010000001b: 'Escape',
    0x0010000007f: 'Delete',
    0x00100000101: 'Accel',
    0x00100000103: 'Alt Graph',
    0x00100000104: 'Caps Lock',
    0x00100000106: 'Fn',
    0x00100000107: 'Fn Lock',
    0x00100000108: 'Hyper',
    0x0010000010a: 'Num Lock',
    0x0010000010c: 'Scroll Lock',
    0x0010000010e: 'Super',
    0x0010000010f: 'Symbol',
    0x00100000110: 'Symbol Lock',
    0x00100000111: 'Shift Level 5',
    0x00100000301: 'Arrow Down',
    0x00100000302: 'Arrow Left',
    0x00100000303: 'Arrow Right',
    0x00100000304: 'Arrow Up',
    0x00100000305: 'End',
    0x00100000306: 'Home',
    0x00100000307: 'Page Down',
    0x00100000308: 'Page Up',
    0x00100000401: 'Clear',
    0x00100000402: 'Copy',
    0x00100000403: 'Cr Sel',
    0x00100000404: 'Cut',
    0x00100000405: 'Erase Eof',
    0x00100000406: 'Ex Sel',
    0x00100000407: 'Insert',
    0x00100000408: 'Paste',
    0x00100000409: 'Redo',
    0x0010000040a: 'Undo',
    0x00100000501: 'Accept',
    0x00100000502: 'Again',
    0x00100000503: 'Attn',
    0x00100000504: 'Cancel',
    0x00100000505: 'Context Menu',
    0x00100000506: 'Execute',
    0x00100000507: 'Find',
    0x00100000508: 'Help',
    0x00100000509: 'Pause',
    0x0010000050a: 'Play',
    0x0010000050b: 'Props',
    0x0010000050c: 'Select',
    0x0010000050d: 'Zoom In',
    0x0010000050e: 'Zoom Out',
    0x00100000601: 'Brightness Down',
    0x00100000602: 'Brightness Up',
    0x00100000603: 'Camera',
    0x00100000604: 'Eject',
    0x00100000605: 'Log Off',
    0x00100000606: 'Power',
    0x00100000607: 'Power Off',
    0x00100000608: 'Print Screen',
    0x00100000609: 'Hibernate',
    0x0010000060a: 'Standby',
    0x0010000060b: 'Wake Up',
    0x00100000701: 'All Candidates',
    0x00100000702: 'Alphanumeric',
    0x00100000703: 'Code Input',
    0x00100000704: 'Compose',
    0x00100000705: 'Convert',
    0x00100000706: 'Final Mode',
    0x00100000707: 'Group First',
    0x00100000708: 'Group Last',
    0x00100000709: 'Group Next',
    0x0010000070a: 'Group Previous',
    0x0010000070b: 'Mode Change',
    0x0010000070c: 'Next Candidate',
    0x0010000070d: 'Non Convert',
    0x0010000070e: 'Previous Candidate',
    0x0010000070f: 'Process',
    0x00100000710: 'Single Candidate',
    0x00100000711: 'Hangul Mode',
    0x00100000712: 'Hanja Mode',
    0x00100000713: 'Junja Mode',
    0x00100000714: 'Eisu',
    0x00100000715: 'Hankaku',
    0x00100000716: 'Hiragana',
    0x00100000717: 'Hiragana Katakana',
    0x00100000718: 'Kana Mode',
    0x00100000719: 'Kanji Mode',
    0x0010000071a: 'Katakana',
    0x0010000071b: 'Romaji',
    0x0010000071c: 'Zenkaku',
    0x0010000071d: 'Zenkaku Hankaku',
    0x00100000801: 'F1',
    0x00100000802: 'F2',
    0x00100000803: 'F3',
    0x00100000804: 'F4',
    0x00100000805: 'F5',
    0x00100000806: 'F6',
    0x00100000807: 'F7',
    0x00100000808: 'F8',
    0x00100000809: 'F9',
    0x0010000080a: 'F10',
    0x0010000080b: 'F11',
    0x0010000080c: 'F12',
    0x0010000080d: 'F13',
    0x0010000080e: 'F14',
    0x0010000080f: 'F15',
    0x00100000810: 'F16',
    0x00100000811: 'F17',
    0x00100000812: 'F18',
    0x00100000813: 'F19',
    0x00100000814: 'F20',
    0x00100000815: 'F21',
    0x00100000816: 'F22',
    0x00100000817: 'F23',
    0x00100000818: 'F24',
    0x00100000901: 'Soft 1',
    0x00100000902: 'Soft 2',
    0x00100000903: 'Soft 3',
    0x00100000904: 'Soft 4',
    0x00100000905: 'Soft 5',
    0x00100000906: 'Soft 6',
    0x00100000907: 'Soft 7',
    0x00100000908: 'Soft 8',
    0x00100000a01: 'Close',
    0x00100000a02: 'Mail Forward',
    0x00100000a03: 'Mail Reply',
    0x00100000a04: 'Mail Send',
    0x00100000a05: 'Media Play Pause',
    0x00100000a07: 'Media Stop',
    0x00100000a08: 'Media Track Next',
    0x00100000a09: 'Media Track Previous',
    0x00100000a0a: 'New',
    0x00100000a0b: 'Open',
    0x00100000a0c: 'Print',
    0x00100000a0d: 'Save',
    0x00100000a0e: 'Spell Check',
    0x00100000a0f: 'Audio Volume Down',
    0x00100000a10: 'Audio Volume Up',
    0x00100000a11: 'Audio Volume Mute',
    0x00100000b01: 'Launch Application 2',
    0x00100000b02: 'Launch Calendar',
    0x00100000b03: 'Launch Mail',
    0x00100000b04: 'Launch Media Player',
    0x00100000b05: 'Launch Music Player',
    0x00100000b06: 'Launch Application 1',
    0x00100000b07: 'Launch Screen Saver',
    0x00100000b08: 'Launch Spreadsheet',
    0x00100000b09: 'Launch Web Browser',
    0x00100000b0a: 'Launch Web Cam',
    0x00100000b0b: 'Launch Word Processor',
    0x00100000b0c: 'Launch Contacts',
    0x00100000b0d: 'Launch Phone',
    0x00100000b0e: 'Launch Assistant',
    0x00100000b0f: 'Launch Control Panel',
    0x00100000c01: 'Browser Back',
    0x00100000c02: 'Browser Favorites',
    0x00100000c03: 'Browser Forward',
    0x00100000c04: 'Browser Home',
    0x00100000c05: 'Browser Refresh',
    0x00100000c06: 'Browser Search',
    0x00100000c07: 'Browser Stop',
    0x00100000d01: 'Audio Balance Left',
    0x00100000d02: 'Audio Balance Right',
    0x00100000d03: 'Audio Bass Boost Down',
    0x00100000d04: 'Audio Bass Boost Up',
    0x00100000d05: 'Audio Fader Front',
    0x00100000d06: 'Audio Fader Rear',
    0x00100000d07: 'Audio Surround Mode Next',
    0x00100000d08: 'AVR Input',
    0x00100000d09: 'AVR Power',
    0x00100000d0a: 'Channel Down',
    0x00100000d0b: 'Channel Up',
    0x00100000d0c: 'Color F0 Red',
    0x00100000d0d: 'Color F1 Green',
    0x00100000d0e: 'Color F2 Yellow',
    0x00100000d0f: 'Color F3 Blue',
    0x00100000d10: 'Color F4 Grey',
    0x00100000d11: 'Color F5 Brown',
    0x00100000d12: 'Closed Caption Toggle',
    0x00100000d13: 'Dimmer',
    0x00100000d14: 'Display Swap',
    0x00100000d15: 'Exit',
    0x00100000d16: 'Favorite Clear 0',
    0x00100000d17: 'Favorite Clear 1',
    0x00100000d18: 'Favorite Clear 2',
    0x00100000d19: 'Favorite Clear 3',
    0x00100000d1a: 'Favorite Recall 0',
    0x00100000d1b: 'Favorite Recall 1',
    0x00100000d1c: 'Favorite Recall 2',
    0x00100000d1d: 'Favorite Recall 3',
    0x00100000d1e: 'Favorite Store 0',
    0x00100000d1f: 'Favorite Store 1',
    0x00100000d20: 'Favorite Store 2',
    0x00100000d21: 'Favorite Store 3',
    0x00100000d22: 'Guide',
    0x00100000d23: 'Guide Next Day',
    0x00100000d24: 'Guide Previous Day',
    0x00100000d25: 'Info',
    0x00100000d26: 'Instant Replay',
    0x00100000d27: 'Link',
    0x00100000d28: 'List Program',
    0x00100000d29: 'Live Content',
    0x00100000d2a: 'Lock',
    0x00100000d2b: 'Media Apps',
    0x00100000d2c: 'Media Fast Forward',
    0x00100000d2d: 'Media Last',
    0x00100000d2e: 'Media Pause',
    0x00100000d2f: 'Media Play',
    0x00100000d30: 'Media Record',
    0x00100000d31: 'Media Rewind',
    0x00100000d32: 'Media Skip',
    0x00100000d33: 'Next Favorite Channel',
    0x00100000d34: 'Next User Profile',
    0x00100000d35: 'On Demand',
    0x00100000d36: 'P In P Down',
    0x00100000d37: 'P In P Move',
    0x00100000d38: 'P In P Toggle',
    0x00100000d39: 'P In P Up',
    0x00100000d3a: 'Play Speed Down',
    0x00100000d3b: 'Play Speed Reset',
    0x00100000d3c: 'Play Speed Up',
    0x00100000d3d: 'Random Toggle',
    0x00100000d3e: 'Rc Low Battery',
    0x00100000d3f: 'Record Speed Next',
    0x00100000d40: 'Rf Bypass',
    0x00100000d41: 'Scan Channels Toggle',
    0x00100000d42: 'Screen Mode Next',
    0x00100000d43: 'Settings',
    0x00100000d44: 'Split Screen Toggle',
    0x00100000d45: 'STB Input',
    0x00100000d46: 'STB Power',
    0x00100000d47: 'Subtitle',
    0x00100000d48: 'Teletext',
    0x00100000d49: 'TV',
    0x00100000d4a: 'TV Input',
    0x00100000d4b: 'TV Power',
    0x00100000d4c: 'Video Mode Next',
    0x00100000d4d: 'Wink',
    0x00100000d4e: 'Zoom Toggle',
    0x00100000d4f: 'DVR',
    0x00100000d50: 'Media Audio Track',
    0x00100000d51: 'Media Skip Backward',
    0x00100000d52: 'Media Skip Forward',
    0x00100000d53: 'Media Step Backward',
    0x00100000d54: 'Media Step Forward',
    0x00100000d55: 'Media Top Menu',
    0x00100000d56: 'Navigate In',
    0x00100000d57: 'Navigate Next',
    0x00100000d58: 'Navigate Out',
    0x00100000d59: 'Navigate Previous',
    0x00100000d5a: 'Pairing',
    0x00100000d5b: 'Media Close',
    0x00100000e02: 'Audio Bass Boost Toggle',
    0x00100000e04: 'Audio Treble Down',
    0x00100000e05: 'Audio Treble Up',
    0x00100000e06: 'Microphone Toggle',
    0x00100000e07: 'Microphone Volume Down',
    0x00100000e08: 'Microphone Volume Up',
    0x00100000e09: 'Microphone Volume Mute',
    0x00100000f01: 'Speech Correction List',
    0x00100000f02: 'Speech Input Toggle',
    0x00100001001: 'App Switch',
    0x00100001002: 'Call',
    0x00100001003: 'Camera Focus',
    0x00100001004: 'End Call',
    0x00100001005: 'Go Back',
    0x00100001006: 'Go Home',
    0x00100001007: 'Headset Hook',
    0x00100001008: 'Last Number Redial',
    0x00100001009: 'Notification',
    0x0010000100a: 'Manner Mode',
    0x0010000100b: 'Voice Dial',
    0x00100001101: 'TV 3 D Mode',
    0x00100001102: 'TV Antenna Cable',
    0x00100001103: 'TV Audio Description',
    0x00100001104: 'TV Audio Description Mix Down',
    0x00100001105: 'TV Audio Description Mix Up',
    0x00100001106: 'TV Contents Menu',
    0x00100001107: 'TV Data Service',
    0x00100001108: 'TV Input Component 1',
    0x00100001109: 'TV Input Component 2',
    0x0010000110a: 'TV Input Composite 1',
    0x0010000110b: 'TV Input Composite 2',
    0x0010000110c: 'TV Input HDMI 1',
    0x0010000110d: 'TV Input HDMI 2',
    0x0010000110e: 'TV Input HDMI 3',
    0x0010000110f: 'TV Input HDMI 4',
    0x00100001110: 'TV Input VGA 1',
    0x00100001111: 'TV Media Context',
    0x00100001112: 'TV Network',
    0x00100001113: 'TV Number Entry',
    0x00100001114: 'TV Radio Service',
    0x00100001115: 'TV Satellite',
    0x00100001116: 'TV Satellite BS',
    0x00100001117: 'TV Satellite CS',
    0x00100001118: 'TV Satellite Toggle',
    0x00100001119: 'TV Terrestrial Analog',
    0x0010000111a: 'TV Terrestrial Digital',
    0x0010000111b: 'TV Timer',
    0x00100001201: 'Key 11',
    0x00100001202: 'Key 12',
    0x00200000000: 'Suspend',
    0x00200000001: 'Resume',
    0x00200000002: 'Sleep',
    0x00200000003: 'Abort',
    0x00200000010: 'Lang 1',
    0x00200000011: 'Lang 2',
    0x00200000012: 'Lang 3',
    0x00200000013: 'Lang 4',
    0x00200000014: 'Lang 5',
    0x00200000020: 'Intl Backslash',
    0x00200000021: 'Intl Ro',
    0x00200000022: 'Intl Yen',
    0x00200000100: 'Control Left',
    0x00200000101: 'Control Right',
    0x00200000102: 'Shift Left',
    0x00200000103: 'Shift Right',
    0x00200000104: 'Alt Left',
    0x00200000105: 'Alt Right',
    0x00200000106: 'Meta Left',
    0x00200000107: 'Meta Right',
    0x002000001f0: 'Control',
    0x002000001f2: 'Shift',
    0x002000001f4: 'Alt',
    0x002000001f6: 'Meta',
    0x0020000020d: 'Numpad Enter',
    0x00200000228: 'Numpad Paren Left',
    0x00200000229: 'Numpad Paren Right',
    0x0020000022a: 'Numpad Multiply',
    0x0020000022b: 'Numpad Add',
    0x0020000022c: 'Numpad Comma',
    0x0020000022d: 'Numpad Subtract',
    0x0020000022e: 'Numpad Decimal',
    0x0020000022f: 'Numpad Divide',
    0x00200000230: 'Numpad 0',
    0x00200000231: 'Numpad 1',
    0x00200000232: 'Numpad 2',
    0x00200000233: 'Numpad 3',
    0x00200000234: 'Numpad 4',
    0x00200000235: 'Numpad 5',
    0x00200000236: 'Numpad 6',
    0x00200000237: 'Numpad 7',
    0x00200000238: 'Numpad 8',
    0x00200000239: 'Numpad 9',
    0x0020000023d: 'Numpad Equal',
    0x00200000301: 'Game Button 1',
    0x00200000302: 'Game Button 2',
    0x00200000303: 'Game Button 3',
    0x00200000304: 'Game Button 4',
    0x00200000305: 'Game Button 5',
    0x00200000306: 'Game Button 6',
    0x00200000307: 'Game Button 7',
    0x00200000308: 'Game Button 8',
    0x00200000309: 'Game Button 9',
    0x0020000030a: 'Game Button 10',
    0x0020000030b: 'Game Button 11',
    0x0020000030c: 'Game Button 12',
    0x0020000030d: 'Game Button 13',
    0x0020000030e: 'Game Button 14',
    0x0020000030f: 'Game Button 15',
    0x00200000310: 'Game Button 16',
    0x00200000311: 'Game Button A',
    0x00200000312: 'Game Button B',
    0x00200000313: 'Game Button C',
    0x00200000314: 'Game Button Left 1',
    0x00200000315: 'Game Button Left 2',
    0x00200000316: 'Game Button Mode',
    0x00200000317: 'Game Button Right 1',
    0x00200000318: 'Game Button Right 2',
    0x00200000319: 'Game Button Select',
    0x0020000031a: 'Game Button Start',
    0x0020000031b: 'Game Button Thumb Left',
    0x0020000031c: 'Game Button Thumb Right',
    0x0020000031d: 'Game Button X',
    0x0020000031e: 'Game Button Y',
    0x0020000031f: 'Game Button Z',
  };
}

/// A class with static values that describe the keys that are returned from
/// [RawKeyEvent.physicalKey].
///
/// These represent *physical* keys, which are keys which represent a particular
/// key location on a QWERTY keyboard. It ignores any modifiers, modes, or
/// keyboard layouts which may be in effect. This is contrast to
/// [LogicalKeyboardKey], which represents a logical key interpreted in the
/// context of modifiers, modes, and/or keyboard layouts.
///
/// As an example, if you wanted a game where the key next to the CAPS LOCK (the
/// "A" key on a QWERTY keyboard) moved the player to the left, you'd want to
/// look at the physical key to make sure that regardless of the character the
/// key produces, you got the key that is in that location on the keyboard.
///
/// Conversely, if you wanted to implement an app where the "Q" key "quit"
/// something, you'd want to look at the logical key to detect this, since you
/// would like to have it match the key with "Q" on it, instead of always
/// looking for "the key next to the TAB key", since on a French keyboard,
/// the key next to the TAB key has an "A" on it.
///
/// {@tool dartpad}
/// This example shows how to detect if the user has selected the physical key
/// to the right of the CAPS LOCK key.
///
/// ** See code in examples/api/lib/services/keyboard_key/physical_keyboard_key.0.dart **
/// {@end-tool}
///
/// See also:
///
///  * [RawKeyEvent], the keyboard event object received by widgets that listen
///    to keyboard events.
///  * [Focus.onKey], the handler on a widget that lets you handle key events.
///  * [RawKeyboardListener], a widget used to listen to keyboard events (but
///    not handle them).
@immutable
class PhysicalKeyboardKey extends KeyboardKey {
  /// Creates a new PhysicalKeyboardKey object for a USB HID usage.
  const PhysicalKeyboardKey(this.usbHidUsage);

  /// The unique USB HID usage ID of this physical key on the keyboard.
  ///
  /// Due to the variations in platform APIs, this may not be the actual HID
  /// usage code from the hardware, but a value derived from available
  /// information on the platform.
  ///
  /// See <https://www.usb.org/sites/default/files/documents/hut1_12v2.pdf>
  /// for the HID usage values and their meanings.
  final int usbHidUsage;

  /// The debug string to print for this keyboard key, which will be null in
  /// release mode.
  String? get debugName {
    String? result;
    assert(() {
      result =
          _debugNames[usbHidUsage] ??
          'Key with ID 0x${usbHidUsage.toRadixString(16).padLeft(8, '0')}';
      return true;
    }());
    return result;
  }

  @override
  int get hashCode => usbHidUsage.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    if (other.runtimeType != runtimeType) {
      return false;
    }
    return other is PhysicalKeyboardKey && other.usbHidUsage == usbHidUsage;
  }

  /// Finds a known [PhysicalKeyboardKey] that matches the given USB HID usage
  /// code.
  static PhysicalKeyboardKey? findKeyByCode(int usageCode) => _knownPhysicalKeys[usageCode];

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
      StringProperty('usbHidUsage', '0x${usbHidUsage.toRadixString(16).padLeft(8, '0')}'),
    );
    properties.add(StringProperty('debugName', debugName, defaultValue: null));
  }

  // Key constants for all keyboard keys in the USB HID specification at the
  // time Flutter was built.

  /// Represents the location of the "Hyper" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey hyper = PhysicalKeyboardKey(0x00000010);

  /// Represents the location of the "Super Key" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey superKey = PhysicalKeyboardKey(0x00000011);

  /// Represents the location of the "Fn" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey fn = PhysicalKeyboardKey(0x00000012);

  /// Represents the location of the "Fn Lock" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey fnLock = PhysicalKeyboardKey(0x00000013);

  /// Represents the location of the "Suspend" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey suspend = PhysicalKeyboardKey(0x00000014);

  /// Represents the location of the "Resume" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey resume = PhysicalKeyboardKey(0x00000015);

  /// Represents the location of the "Turbo" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey turbo = PhysicalKeyboardKey(0x00000016);

  /// Represents the location of the "Privacy Screen Toggle" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey privacyScreenToggle = PhysicalKeyboardKey(0x00000017);

  /// Represents the location of the "Microphone Mute Toggle" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey microphoneMuteToggle = PhysicalKeyboardKey(0x00000018);

  /// Represents the location of the "Sleep" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey sleep = PhysicalKeyboardKey(0x00010082);

  /// Represents the location of the "Wake Up" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey wakeUp = PhysicalKeyboardKey(0x00010083);

  /// Represents the location of the "Display Toggle Int Ext" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey displayToggleIntExt = PhysicalKeyboardKey(0x000100b5);

  /// Represents the location of the "Game Button 1" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton1 = PhysicalKeyboardKey(0x0005ff01);

  /// Represents the location of the "Game Button 2" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton2 = PhysicalKeyboardKey(0x0005ff02);

  /// Represents the location of the "Game Button 3" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton3 = PhysicalKeyboardKey(0x0005ff03);

  /// Represents the location of the "Game Button 4" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton4 = PhysicalKeyboardKey(0x0005ff04);

  /// Represents the location of the "Game Button 5" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton5 = PhysicalKeyboardKey(0x0005ff05);

  /// Represents the location of the "Game Button 6" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton6 = PhysicalKeyboardKey(0x0005ff06);

  /// Represents the location of the "Game Button 7" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton7 = PhysicalKeyboardKey(0x0005ff07);

  /// Represents the location of the "Game Button 8" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton8 = PhysicalKeyboardKey(0x0005ff08);

  /// Represents the location of the "Game Button 9" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton9 = PhysicalKeyboardKey(0x0005ff09);

  /// Represents the location of the "Game Button 10" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton10 = PhysicalKeyboardKey(0x0005ff0a);

  /// Represents the location of the "Game Button 11" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton11 = PhysicalKeyboardKey(0x0005ff0b);

  /// Represents the location of the "Game Button 12" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton12 = PhysicalKeyboardKey(0x0005ff0c);

  /// Represents the location of the "Game Button 13" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton13 = PhysicalKeyboardKey(0x0005ff0d);

  /// Represents the location of the "Game Button 14" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton14 = PhysicalKeyboardKey(0x0005ff0e);

  /// Represents the location of the "Game Button 15" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton15 = PhysicalKeyboardKey(0x0005ff0f);

  /// Represents the location of the "Game Button 16" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButton16 = PhysicalKeyboardKey(0x0005ff10);

  /// Represents the location of the "Game Button A" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonA = PhysicalKeyboardKey(0x0005ff11);

  /// Represents the location of the "Game Button B" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonB = PhysicalKeyboardKey(0x0005ff12);

  /// Represents the location of the "Game Button C" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonC = PhysicalKeyboardKey(0x0005ff13);

  /// Represents the location of the "Game Button Left 1" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonLeft1 = PhysicalKeyboardKey(0x0005ff14);

  /// Represents the location of the "Game Button Left 2" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonLeft2 = PhysicalKeyboardKey(0x0005ff15);

  /// Represents the location of the "Game Button Mode" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonMode = PhysicalKeyboardKey(0x0005ff16);

  /// Represents the location of the "Game Button Right 1" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonRight1 = PhysicalKeyboardKey(0x0005ff17);

  /// Represents the location of the "Game Button Right 2" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonRight2 = PhysicalKeyboardKey(0x0005ff18);

  /// Represents the location of the "Game Button Select" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonSelect = PhysicalKeyboardKey(0x0005ff19);

  /// Represents the location of the "Game Button Start" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonStart = PhysicalKeyboardKey(0x0005ff1a);

  /// Represents the location of the "Game Button Thumb Left" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonThumbLeft = PhysicalKeyboardKey(0x0005ff1b);

  /// Represents the location of the "Game Button Thumb Right" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonThumbRight = PhysicalKeyboardKey(0x0005ff1c);

  /// Represents the location of the "Game Button X" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonX = PhysicalKeyboardKey(0x0005ff1d);

  /// Represents the location of the "Game Button Y" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonY = PhysicalKeyboardKey(0x0005ff1e);

  /// Represents the location of the "Game Button Z" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey gameButtonZ = PhysicalKeyboardKey(0x0005ff1f);

  /// Represents the location of the "Usb Reserved" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey usbReserved = PhysicalKeyboardKey(0x00070000);

  /// Represents the location of the "Usb Error Roll Over" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey usbErrorRollOver = PhysicalKeyboardKey(0x00070001);

  /// Represents the location of the "Usb Post Fail" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey usbPostFail = PhysicalKeyboardKey(0x00070002);

  /// Represents the location of the "Usb Error Undefined" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey usbErrorUndefined = PhysicalKeyboardKey(0x00070003);

  /// Represents the location of the "Key A" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyA = PhysicalKeyboardKey(0x00070004);

  /// Represents the location of the "Key B" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyB = PhysicalKeyboardKey(0x00070005);

  /// Represents the location of the "Key C" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyC = PhysicalKeyboardKey(0x00070006);

  /// Represents the location of the "Key D" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyD = PhysicalKeyboardKey(0x00070007);

  /// Represents the location of the "Key E" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyE = PhysicalKeyboardKey(0x00070008);

  /// Represents the location of the "Key F" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyF = PhysicalKeyboardKey(0x00070009);

  /// Represents the location of the "Key G" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyG = PhysicalKeyboardKey(0x0007000a);

  /// Represents the location of the "Key H" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyH = PhysicalKeyboardKey(0x0007000b);

  /// Represents the location of the "Key I" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyI = PhysicalKeyboardKey(0x0007000c);

  /// Represents the location of the "Key J" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyJ = PhysicalKeyboardKey(0x0007000d);

  /// Represents the location of the "Key K" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyK = PhysicalKeyboardKey(0x0007000e);

  /// Represents the location of the "Key L" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyL = PhysicalKeyboardKey(0x0007000f);

  /// Represents the location of the "Key M" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyM = PhysicalKeyboardKey(0x00070010);

  /// Represents the location of the "Key N" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyN = PhysicalKeyboardKey(0x00070011);

  /// Represents the location of the "Key O" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyO = PhysicalKeyboardKey(0x00070012);

  /// Represents the location of the "Key P" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyP = PhysicalKeyboardKey(0x00070013);

  /// Represents the location of the "Key Q" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyQ = PhysicalKeyboardKey(0x00070014);

  /// Represents the location of the "Key R" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyR = PhysicalKeyboardKey(0x00070015);

  /// Represents the location of the "Key S" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyS = PhysicalKeyboardKey(0x00070016);

  /// Represents the location of the "Key T" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyT = PhysicalKeyboardKey(0x00070017);

  /// Represents the location of the "Key U" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyU = PhysicalKeyboardKey(0x00070018);

  /// Represents the location of the "Key V" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyV = PhysicalKeyboardKey(0x00070019);

  /// Represents the location of the "Key W" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyW = PhysicalKeyboardKey(0x0007001a);

  /// Represents the location of the "Key X" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyX = PhysicalKeyboardKey(0x0007001b);

  /// Represents the location of the "Key Y" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyY = PhysicalKeyboardKey(0x0007001c);

  /// Represents the location of the "Key Z" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyZ = PhysicalKeyboardKey(0x0007001d);

  /// Represents the location of the "Digit 1" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit1 = PhysicalKeyboardKey(0x0007001e);

  /// Represents the location of the "Digit 2" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit2 = PhysicalKeyboardKey(0x0007001f);

  /// Represents the location of the "Digit 3" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit3 = PhysicalKeyboardKey(0x00070020);

  /// Represents the location of the "Digit 4" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit4 = PhysicalKeyboardKey(0x00070021);

  /// Represents the location of the "Digit 5" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit5 = PhysicalKeyboardKey(0x00070022);

  /// Represents the location of the "Digit 6" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit6 = PhysicalKeyboardKey(0x00070023);

  /// Represents the location of the "Digit 7" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit7 = PhysicalKeyboardKey(0x00070024);

  /// Represents the location of the "Digit 8" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit8 = PhysicalKeyboardKey(0x00070025);

  /// Represents the location of the "Digit 9" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit9 = PhysicalKeyboardKey(0x00070026);

  /// Represents the location of the "Digit 0" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey digit0 = PhysicalKeyboardKey(0x00070027);

  /// Represents the location of the "Enter" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey enter = PhysicalKeyboardKey(0x00070028);

  /// Represents the location of the "Escape" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey escape = PhysicalKeyboardKey(0x00070029);

  /// Represents the location of the "Backspace" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey backspace = PhysicalKeyboardKey(0x0007002a);

  /// Represents the location of the "Tab" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey tab = PhysicalKeyboardKey(0x0007002b);

  /// Represents the location of the "Space" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey space = PhysicalKeyboardKey(0x0007002c);

  /// Represents the location of the "Minus" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey minus = PhysicalKeyboardKey(0x0007002d);

  /// Represents the location of the "Equal" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey equal = PhysicalKeyboardKey(0x0007002e);

  /// Represents the location of the "Bracket Left" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey bracketLeft = PhysicalKeyboardKey(0x0007002f);

  /// Represents the location of the "Bracket Right" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey bracketRight = PhysicalKeyboardKey(0x00070030);

  /// Represents the location of the "Backslash" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey backslash = PhysicalKeyboardKey(0x00070031);

  /// Represents the location of the "Semicolon" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey semicolon = PhysicalKeyboardKey(0x00070033);

  /// Represents the location of the "Quote" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey quote = PhysicalKeyboardKey(0x00070034);

  /// Represents the location of the "Backquote" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey backquote = PhysicalKeyboardKey(0x00070035);

  /// Represents the location of the "Comma" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey comma = PhysicalKeyboardKey(0x00070036);

  /// Represents the location of the "Period" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey period = PhysicalKeyboardKey(0x00070037);

  /// Represents the location of the "Slash" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey slash = PhysicalKeyboardKey(0x00070038);

  /// Represents the location of the "Caps Lock" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey capsLock = PhysicalKeyboardKey(0x00070039);

  /// Represents the location of the "F1" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f1 = PhysicalKeyboardKey(0x0007003a);

  /// Represents the location of the "F2" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f2 = PhysicalKeyboardKey(0x0007003b);

  /// Represents the location of the "F3" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f3 = PhysicalKeyboardKey(0x0007003c);

  /// Represents the location of the "F4" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f4 = PhysicalKeyboardKey(0x0007003d);

  /// Represents the location of the "F5" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f5 = PhysicalKeyboardKey(0x0007003e);

  /// Represents the location of the "F6" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f6 = PhysicalKeyboardKey(0x0007003f);

  /// Represents the location of the "F7" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f7 = PhysicalKeyboardKey(0x00070040);

  /// Represents the location of the "F8" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f8 = PhysicalKeyboardKey(0x00070041);

  /// Represents the location of the "F9" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f9 = PhysicalKeyboardKey(0x00070042);

  /// Represents the location of the "F10" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f10 = PhysicalKeyboardKey(0x00070043);

  /// Represents the location of the "F11" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f11 = PhysicalKeyboardKey(0x00070044);

  /// Represents the location of the "F12" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f12 = PhysicalKeyboardKey(0x00070045);

  /// Represents the location of the "Print Screen" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey printScreen = PhysicalKeyboardKey(0x00070046);

  /// Represents the location of the "Scroll Lock" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey scrollLock = PhysicalKeyboardKey(0x00070047);

  /// Represents the location of the "Pause" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey pause = PhysicalKeyboardKey(0x00070048);

  /// Represents the location of the "Insert" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey insert = PhysicalKeyboardKey(0x00070049);

  /// Represents the location of the "Home" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey home = PhysicalKeyboardKey(0x0007004a);

  /// Represents the location of the "Page Up" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey pageUp = PhysicalKeyboardKey(0x0007004b);

  /// Represents the location of the "Delete" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey delete = PhysicalKeyboardKey(0x0007004c);

  /// Represents the location of the "End" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey end = PhysicalKeyboardKey(0x0007004d);

  /// Represents the location of the "Page Down" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey pageDown = PhysicalKeyboardKey(0x0007004e);

  /// Represents the location of the "Arrow Right" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey arrowRight = PhysicalKeyboardKey(0x0007004f);

  /// Represents the location of the "Arrow Left" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey arrowLeft = PhysicalKeyboardKey(0x00070050);

  /// Represents the location of the "Arrow Down" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey arrowDown = PhysicalKeyboardKey(0x00070051);

  /// Represents the location of the "Arrow Up" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey arrowUp = PhysicalKeyboardKey(0x00070052);

  /// Represents the location of the "Num Lock" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numLock = PhysicalKeyboardKey(0x00070053);

  /// Represents the location of the "Numpad Divide" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadDivide = PhysicalKeyboardKey(0x00070054);

  /// Represents the location of the "Numpad Multiply" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMultiply = PhysicalKeyboardKey(0x00070055);

  /// Represents the location of the "Numpad Subtract" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadSubtract = PhysicalKeyboardKey(0x00070056);

  /// Represents the location of the "Numpad Add" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadAdd = PhysicalKeyboardKey(0x00070057);

  /// Represents the location of the "Numpad Enter" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadEnter = PhysicalKeyboardKey(0x00070058);

  /// Represents the location of the "Numpad 1" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad1 = PhysicalKeyboardKey(0x00070059);

  /// Represents the location of the "Numpad 2" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad2 = PhysicalKeyboardKey(0x0007005a);

  /// Represents the location of the "Numpad 3" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad3 = PhysicalKeyboardKey(0x0007005b);

  /// Represents the location of the "Numpad 4" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad4 = PhysicalKeyboardKey(0x0007005c);

  /// Represents the location of the "Numpad 5" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad5 = PhysicalKeyboardKey(0x0007005d);

  /// Represents the location of the "Numpad 6" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad6 = PhysicalKeyboardKey(0x0007005e);

  /// Represents the location of the "Numpad 7" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad7 = PhysicalKeyboardKey(0x0007005f);

  /// Represents the location of the "Numpad 8" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad8 = PhysicalKeyboardKey(0x00070060);

  /// Represents the location of the "Numpad 9" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad9 = PhysicalKeyboardKey(0x00070061);

  /// Represents the location of the "Numpad 0" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpad0 = PhysicalKeyboardKey(0x00070062);

  /// Represents the location of the "Numpad Decimal" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadDecimal = PhysicalKeyboardKey(0x00070063);

  /// Represents the location of the "Intl Backslash" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey intlBackslash = PhysicalKeyboardKey(0x00070064);

  /// Represents the location of the "Context Menu" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey contextMenu = PhysicalKeyboardKey(0x00070065);

  /// Represents the location of the "Power" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey power = PhysicalKeyboardKey(0x00070066);

  /// Represents the location of the "Numpad Equal" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadEqual = PhysicalKeyboardKey(0x00070067);

  /// Represents the location of the "F13" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f13 = PhysicalKeyboardKey(0x00070068);

  /// Represents the location of the "F14" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f14 = PhysicalKeyboardKey(0x00070069);

  /// Represents the location of the "F15" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f15 = PhysicalKeyboardKey(0x0007006a);

  /// Represents the location of the "F16" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f16 = PhysicalKeyboardKey(0x0007006b);

  /// Represents the location of the "F17" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f17 = PhysicalKeyboardKey(0x0007006c);

  /// Represents the location of the "F18" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f18 = PhysicalKeyboardKey(0x0007006d);

  /// Represents the location of the "F19" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f19 = PhysicalKeyboardKey(0x0007006e);

  /// Represents the location of the "F20" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f20 = PhysicalKeyboardKey(0x0007006f);

  /// Represents the location of the "F21" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f21 = PhysicalKeyboardKey(0x00070070);

  /// Represents the location of the "F22" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f22 = PhysicalKeyboardKey(0x00070071);

  /// Represents the location of the "F23" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f23 = PhysicalKeyboardKey(0x00070072);

  /// Represents the location of the "F24" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey f24 = PhysicalKeyboardKey(0x00070073);

  /// Represents the location of the "Open" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey open = PhysicalKeyboardKey(0x00070074);

  /// Represents the location of the "Help" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey help = PhysicalKeyboardKey(0x00070075);

  /// Represents the location of the "Select" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey select = PhysicalKeyboardKey(0x00070077);

  /// Represents the location of the "Again" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey again = PhysicalKeyboardKey(0x00070079);

  /// Represents the location of the "Undo" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey undo = PhysicalKeyboardKey(0x0007007a);

  /// Represents the location of the "Cut" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey cut = PhysicalKeyboardKey(0x0007007b);

  /// Represents the location of the "Copy" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey copy = PhysicalKeyboardKey(0x0007007c);

  /// Represents the location of the "Paste" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey paste = PhysicalKeyboardKey(0x0007007d);

  /// Represents the location of the "Find" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey find = PhysicalKeyboardKey(0x0007007e);

  /// Represents the location of the "Audio Volume Mute" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey audioVolumeMute = PhysicalKeyboardKey(0x0007007f);

  /// Represents the location of the "Audio Volume Up" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey audioVolumeUp = PhysicalKeyboardKey(0x00070080);

  /// Represents the location of the "Audio Volume Down" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey audioVolumeDown = PhysicalKeyboardKey(0x00070081);

  /// Represents the location of the "Numpad Comma" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadComma = PhysicalKeyboardKey(0x00070085);

  /// Represents the location of the "Intl Ro" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey intlRo = PhysicalKeyboardKey(0x00070087);

  /// Represents the location of the "Kana Mode" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey kanaMode = PhysicalKeyboardKey(0x00070088);

  /// Represents the location of the "Intl Yen" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey intlYen = PhysicalKeyboardKey(0x00070089);

  /// Represents the location of the "Convert" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey convert = PhysicalKeyboardKey(0x0007008a);

  /// Represents the location of the "Non Convert" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey nonConvert = PhysicalKeyboardKey(0x0007008b);

  /// Represents the location of the "Lang 1" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lang1 = PhysicalKeyboardKey(0x00070090);

  /// Represents the location of the "Lang 2" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lang2 = PhysicalKeyboardKey(0x00070091);

  /// Represents the location of the "Lang 3" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lang3 = PhysicalKeyboardKey(0x00070092);

  /// Represents the location of the "Lang 4" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lang4 = PhysicalKeyboardKey(0x00070093);

  /// Represents the location of the "Lang 5" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lang5 = PhysicalKeyboardKey(0x00070094);

  /// Represents the location of the "Abort" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey abort = PhysicalKeyboardKey(0x0007009b);

  /// Represents the location of the "Props" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey props = PhysicalKeyboardKey(0x000700a3);

  /// Represents the location of the "Numpad Paren Left" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadParenLeft = PhysicalKeyboardKey(0x000700b6);

  /// Represents the location of the "Numpad Paren Right" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadParenRight = PhysicalKeyboardKey(0x000700b7);

  /// Represents the location of the "Numpad Backspace" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadBackspace = PhysicalKeyboardKey(0x000700bb);

  /// Represents the location of the "Numpad Memory Store" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMemoryStore = PhysicalKeyboardKey(0x000700d0);

  /// Represents the location of the "Numpad Memory Recall" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMemoryRecall = PhysicalKeyboardKey(0x000700d1);

  /// Represents the location of the "Numpad Memory Clear" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMemoryClear = PhysicalKeyboardKey(0x000700d2);

  /// Represents the location of the "Numpad Memory Add" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMemoryAdd = PhysicalKeyboardKey(0x000700d3);

  /// Represents the location of the "Numpad Memory Subtract" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadMemorySubtract = PhysicalKeyboardKey(0x000700d4);

  /// Represents the location of the "Numpad Sign Change" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadSignChange = PhysicalKeyboardKey(0x000700d7);

  /// Represents the location of the "Numpad Clear" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadClear = PhysicalKeyboardKey(0x000700d8);

  /// Represents the location of the "Numpad Clear Entry" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey numpadClearEntry = PhysicalKeyboardKey(0x000700d9);

  /// Represents the location of the "Control Left" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey controlLeft = PhysicalKeyboardKey(0x000700e0);

  /// Represents the location of the "Shift Left" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey shiftLeft = PhysicalKeyboardKey(0x000700e1);

  /// Represents the location of the "Alt Left" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey altLeft = PhysicalKeyboardKey(0x000700e2);

  /// Represents the location of the "Meta Left" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey metaLeft = PhysicalKeyboardKey(0x000700e3);

  /// Represents the location of the "Control Right" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey controlRight = PhysicalKeyboardKey(0x000700e4);

  /// Represents the location of the "Shift Right" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey shiftRight = PhysicalKeyboardKey(0x000700e5);

  /// Represents the location of the "Alt Right" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey altRight = PhysicalKeyboardKey(0x000700e6);

  /// Represents the location of the "Meta Right" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey metaRight = PhysicalKeyboardKey(0x000700e7);

  /// Represents the location of the "Info" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey info = PhysicalKeyboardKey(0x000c0060);

  /// Represents the location of the "Closed Caption Toggle" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey closedCaptionToggle = PhysicalKeyboardKey(0x000c0061);

  /// Represents the location of the "Brightness Up" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessUp = PhysicalKeyboardKey(0x000c006f);

  /// Represents the location of the "Brightness Down" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessDown = PhysicalKeyboardKey(0x000c0070);

  /// Represents the location of the "Brightness Toggle" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessToggle = PhysicalKeyboardKey(0x000c0072);

  /// Represents the location of the "Brightness Minimum" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessMinimum = PhysicalKeyboardKey(0x000c0073);

  /// Represents the location of the "Brightness Maximum" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessMaximum = PhysicalKeyboardKey(0x000c0074);

  /// Represents the location of the "Brightness Auto" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey brightnessAuto = PhysicalKeyboardKey(0x000c0075);

  /// Represents the location of the "Kbd Illum Up" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey kbdIllumUp = PhysicalKeyboardKey(0x000c0079);

  /// Represents the location of the "Kbd Illum Down" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey kbdIllumDown = PhysicalKeyboardKey(0x000c007a);

  /// Represents the location of the "Media Last" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaLast = PhysicalKeyboardKey(0x000c0083);

  /// Represents the location of the "Launch Phone" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchPhone = PhysicalKeyboardKey(0x000c008c);

  /// Represents the location of the "Program Guide" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey programGuide = PhysicalKeyboardKey(0x000c008d);

  /// Represents the location of the "Exit" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey exit = PhysicalKeyboardKey(0x000c0094);

  /// Represents the location of the "Channel Up" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey channelUp = PhysicalKeyboardKey(0x000c009c);

  /// Represents the location of the "Channel Down" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey channelDown = PhysicalKeyboardKey(0x000c009d);

  /// Represents the location of the "Media Play" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaPlay = PhysicalKeyboardKey(0x000c00b0);

  /// Represents the location of the "Media Pause" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaPause = PhysicalKeyboardKey(0x000c00b1);

  /// Represents the location of the "Media Record" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaRecord = PhysicalKeyboardKey(0x000c00b2);

  /// Represents the location of the "Media Fast Forward" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaFastForward = PhysicalKeyboardKey(0x000c00b3);

  /// Represents the location of the "Media Rewind" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaRewind = PhysicalKeyboardKey(0x000c00b4);

  /// Represents the location of the "Media Track Next" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaTrackNext = PhysicalKeyboardKey(0x000c00b5);

  /// Represents the location of the "Media Track Previous" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaTrackPrevious = PhysicalKeyboardKey(0x000c00b6);

  /// Represents the location of the "Media Stop" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaStop = PhysicalKeyboardKey(0x000c00b7);

  /// Represents the location of the "Eject" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey eject = PhysicalKeyboardKey(0x000c00b8);

  /// Represents the location of the "Media Play Pause" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaPlayPause = PhysicalKeyboardKey(0x000c00cd);

  /// Represents the location of the "Speech Input Toggle" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey speechInputToggle = PhysicalKeyboardKey(0x000c00cf);

  /// Represents the location of the "Bass Boost" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey bassBoost = PhysicalKeyboardKey(0x000c00e5);

  /// Represents the location of the "Media Select" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mediaSelect = PhysicalKeyboardKey(0x000c0183);

  /// Represents the location of the "Launch Word Processor" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchWordProcessor = PhysicalKeyboardKey(0x000c0184);

  /// Represents the location of the "Launch Spreadsheet" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchSpreadsheet = PhysicalKeyboardKey(0x000c0186);

  /// Represents the location of the "Launch Mail" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchMail = PhysicalKeyboardKey(0x000c018a);

  /// Represents the location of the "Launch Contacts" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchContacts = PhysicalKeyboardKey(0x000c018d);

  /// Represents the location of the "Launch Calendar" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchCalendar = PhysicalKeyboardKey(0x000c018e);

  /// Represents the location of the "Launch App2" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchApp2 = PhysicalKeyboardKey(0x000c0192);

  /// Represents the location of the "Launch App1" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchApp1 = PhysicalKeyboardKey(0x000c0194);

  /// Represents the location of the "Launch Internet Browser" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchInternetBrowser = PhysicalKeyboardKey(0x000c0196);

  /// Represents the location of the "Log Off" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey logOff = PhysicalKeyboardKey(0x000c019c);

  /// Represents the location of the "Lock Screen" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey lockScreen = PhysicalKeyboardKey(0x000c019e);

  /// Represents the location of the "Launch Control Panel" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchControlPanel = PhysicalKeyboardKey(0x000c019f);

  /// Represents the location of the "Select Task" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey selectTask = PhysicalKeyboardKey(0x000c01a2);

  /// Represents the location of the "Launch Documents" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchDocuments = PhysicalKeyboardKey(0x000c01a7);

  /// Represents the location of the "Spell Check" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey spellCheck = PhysicalKeyboardKey(0x000c01ab);

  /// Represents the location of the "Launch Keyboard Layout" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchKeyboardLayout = PhysicalKeyboardKey(0x000c01ae);

  /// Represents the location of the "Launch Screen Saver" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchScreenSaver = PhysicalKeyboardKey(0x000c01b1);

  /// Represents the location of the "Launch Audio Browser" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchAudioBrowser = PhysicalKeyboardKey(0x000c01b7);

  /// Represents the location of the "Launch Assistant" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey launchAssistant = PhysicalKeyboardKey(0x000c01cb);

  /// Represents the location of the "New Key" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey newKey = PhysicalKeyboardKey(0x000c0201);

  /// Represents the location of the "Close" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey close = PhysicalKeyboardKey(0x000c0203);

  /// Represents the location of the "Save" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey save = PhysicalKeyboardKey(0x000c0207);

  /// Represents the location of the "Print" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey print = PhysicalKeyboardKey(0x000c0208);

  /// Represents the location of the "Browser Search" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserSearch = PhysicalKeyboardKey(0x000c0221);

  /// Represents the location of the "Browser Home" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserHome = PhysicalKeyboardKey(0x000c0223);

  /// Represents the location of the "Browser Back" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserBack = PhysicalKeyboardKey(0x000c0224);

  /// Represents the location of the "Browser Forward" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserForward = PhysicalKeyboardKey(0x000c0225);

  /// Represents the location of the "Browser Stop" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserStop = PhysicalKeyboardKey(0x000c0226);

  /// Represents the location of the "Browser Refresh" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserRefresh = PhysicalKeyboardKey(0x000c0227);

  /// Represents the location of the "Browser Favorites" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey browserFavorites = PhysicalKeyboardKey(0x000c022a);

  /// Represents the location of the "Zoom In" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey zoomIn = PhysicalKeyboardKey(0x000c022d);

  /// Represents the location of the "Zoom Out" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey zoomOut = PhysicalKeyboardKey(0x000c022e);

  /// Represents the location of the "Zoom Toggle" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey zoomToggle = PhysicalKeyboardKey(0x000c0232);

  /// Represents the location of the "Redo" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey redo = PhysicalKeyboardKey(0x000c0279);

  /// Represents the location of the "Mail Reply" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mailReply = PhysicalKeyboardKey(0x000c0289);

  /// Represents the location of the "Mail Forward" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mailForward = PhysicalKeyboardKey(0x000c028b);

  /// Represents the location of the "Mail Send" key on a generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey mailSend = PhysicalKeyboardKey(0x000c028c);

  /// Represents the location of the "Keyboard Layout Select" key on a
  /// generalized keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey keyboardLayoutSelect = PhysicalKeyboardKey(0x000c029d);

  /// Represents the location of the "Show All Windows" key on a generalized
  /// keyboard.
  ///
  /// See the function [RawKeyEvent.physicalKey] for more information.
  static const PhysicalKeyboardKey showAllWindows = PhysicalKeyboardKey(0x000c029f);

  /// A list of all predefined constant [PhysicalKeyboardKey]s.
  static Iterable<PhysicalKeyboardKey> get knownPhysicalKeys => _knownPhysicalKeys.values;

  // A list of all the predefined constant PhysicalKeyboardKeys so that they
  // can be searched.
  static const Map<int, PhysicalKeyboardKey> _knownPhysicalKeys = <int, PhysicalKeyboardKey>{
    0x00000010: hyper,
    0x00000011: superKey,
    0x00000012: fn,
    0x00000013: fnLock,
    0x00000014: suspend,
    0x00000015: resume,
    0x00000016: turbo,
    0x00000017: privacyScreenToggle,
    0x00000018: microphoneMuteToggle,
    0x00010082: sleep,
    0x00010083: wakeUp,
    0x000100b5: displayToggleIntExt,
    0x0005ff01: gameButton1,
    0x0005ff02: gameButton2,
    0x0005ff03: gameButton3,
    0x0005ff04: gameButton4,
    0x0005ff05: gameButton5,
    0x0005ff06: gameButton6,
    0x0005ff07: gameButton7,
    0x0005ff08: gameButton8,
    0x0005ff09: gameButton9,
    0x0005ff0a: gameButton10,
    0x0005ff0b: gameButton11,
    0x0005ff0c: gameButton12,
    0x0005ff0d: gameButton13,
    0x0005ff0e: gameButton14,
    0x0005ff0f: gameButton15,
    0x0005ff10: gameButton16,
    0x0005ff11: gameButtonA,
    0x0005ff12: gameButtonB,
    0x0005ff13: gameButtonC,
    0x0005ff14: gameButtonLeft1,
    0x0005ff15: gameButtonLeft2,
    0x0005ff16: gameButtonMode,
    0x0005ff17: gameButtonRight1,
    0x0005ff18: gameButtonRight2,
    0x0005ff19: gameButtonSelect,
    0x0005ff1a: gameButtonStart,
    0x0005ff1b: gameButtonThumbLeft,
    0x0005ff1c: gameButtonThumbRight,
    0x0005ff1d: gameButtonX,
    0x0005ff1e: gameButtonY,
    0x0005ff1f: gameButtonZ,
    0x00070000: usbReserved,
    0x00070001: usbErrorRollOver,
    0x00070002: usbPostFail,
    0x00070003: usbErrorUndefined,
    0x00070004: keyA,
    0x00070005: keyB,
    0x00070006: keyC,
    0x00070007: keyD,
    0x00070008: keyE,
    0x00070009: keyF,
    0x0007000a: keyG,
    0x0007000b: keyH,
    0x0007000c: keyI,
    0x0007000d: keyJ,
    0x0007000e: keyK,
    0x0007000f: keyL,
    0x00070010: keyM,
    0x00070011: keyN,
    0x00070012: keyO,
    0x00070013: keyP,
    0x00070014: keyQ,
    0x00070015: keyR,
    0x00070016: keyS,
    0x00070017: keyT,
    0x00070018: keyU,
    0x00070019: keyV,
    0x0007001a: keyW,
    0x0007001b: keyX,
    0x0007001c: keyY,
    0x0007001d: keyZ,
    0x0007001e: digit1,
    0x0007001f: digit2,
    0x00070020: digit3,
    0x00070021: digit4,
    0x00070022: digit5,
    0x00070023: digit6,
    0x00070024: digit7,
    0x00070025: digit8,
    0x00070026: digit9,
    0x00070027: digit0,
    0x00070028: enter,
    0x00070029: escape,
    0x0007002a: backspace,
    0x0007002b: tab,
    0x0007002c: space,
    0x0007002d: minus,
    0x0007002e: equal,
    0x0007002f: bracketLeft,
    0x00070030: bracketRight,
    0x00070031: backslash,
    0x00070033: semicolon,
    0x00070034: quote,
    0x00070035: backquote,
    0x00070036: comma,
    0x00070037: period,
    0x00070038: slash,
    0x00070039: capsLock,
    0x0007003a: f1,
    0x0007003b: f2,
    0x0007003c: f3,
    0x0007003d: f4,
    0x0007003e: f5,
    0x0007003f: f6,
    0x00070040: f7,
    0x00070041: f8,
    0x00070042: f9,
    0x00070043: f10,
    0x00070044: f11,
    0x00070045: f12,
    0x00070046: printScreen,
    0x00070047: scrollLock,
    0x00070048: pause,
    0x00070049: insert,
    0x0007004a: home,
    0x0007004b: pageUp,
    0x0007004c: delete,
    0x0007004d: end,
    0x0007004e: pageDown,
    0x0007004f: arrowRight,
    0x00070050: arrowLeft,
    0x00070051: arrowDown,
    0x00070052: arrowUp,
    0x00070053: numLock,
    0x00070054: numpadDivide,
    0x00070055: numpadMultiply,
    0x00070056: numpadSubtract,
    0x00070057: numpadAdd,
    0x00070058: numpadEnter,
    0x00070059: numpad1,
    0x0007005a: numpad2,
    0x0007005b: numpad3,
    0x0007005c: numpad4,
    0x0007005d: numpad5,
    0x0007005e: numpad6,
    0x0007005f: numpad7,
    0x00070060: numpad8,
    0x00070061: numpad9,
    0x00070062: numpad0,
    0x00070063: numpadDecimal,
    0x00070064: intlBackslash,
    0x00070065: contextMenu,
    0x00070066: power,
    0x00070067: numpadEqual,
    0x00070068: f13,
    0x00070069: f14,
    0x0007006a: f15,
    0x0007006b: f16,
    0x0007006c: f17,
    0x0007006d: f18,
    0x0007006e: f19,
    0x0007006f: f20,
    0x00070070: f21,
    0x00070071: f22,
    0x00070072: f23,
    0x00070073: f24,
    0x00070074: open,
    0x00070075: help,
    0x00070077: select,
    0x00070079: again,
    0x0007007a: undo,
    0x0007007b: cut,
    0x0007007c: copy,
    0x0007007d: paste,
    0x0007007e: find,
    0x0007007f: audioVolumeMute,
    0x00070080: audioVolumeUp,
    0x00070081: audioVolumeDown,
    0x00070085: numpadComma,
    0x00070087: intlRo,
    0x00070088: kanaMode,
    0x00070089: intlYen,
    0x0007008a: convert,
    0x0007008b: nonConvert,
    0x00070090: lang1,
    0x00070091: lang2,
    0x00070092: lang3,
    0x00070093: lang4,
    0x00070094: lang5,
    0x0007009b: abort,
    0x000700a3: props,
    0x000700b6: numpadParenLeft,
    0x000700b7: numpadParenRight,
    0x000700bb: numpadBackspace,
    0x000700d0: numpadMemoryStore,
    0x000700d1: numpadMemoryRecall,
    0x000700d2: numpadMemoryClear,
    0x000700d3: numpadMemoryAdd,
    0x000700d4: numpadMemorySubtract,
    0x000700d7: numpadSignChange,
    0x000700d8: numpadClear,
    0x000700d9: numpadClearEntry,
    0x000700e0: controlLeft,
    0x000700e1: shiftLeft,
    0x000700e2: altLeft,
    0x000700e3: metaLeft,
    0x000700e4: controlRight,
    0x000700e5: shiftRight,
    0x000700e6: altRight,
    0x000700e7: metaRight,
    0x000c0060: info,
    0x000c0061: closedCaptionToggle,
    0x000c006f: brightnessUp,
    0x000c0070: brightnessDown,
    0x000c0072: brightnessToggle,
    0x000c0073: brightnessMinimum,
    0x000c0074: brightnessMaximum,
    0x000c0075: brightnessAuto,
    0x000c0079: kbdIllumUp,
    0x000c007a: kbdIllumDown,
    0x000c0083: mediaLast,
    0x000c008c: launchPhone,
    0x000c008d: programGuide,
    0x000c0094: exit,
    0x000c009c: channelUp,
    0x000c009d: channelDown,
    0x000c00b0: mediaPlay,
    0x000c00b1: mediaPause,
    0x000c00b2: mediaRecord,
    0x000c00b3: mediaFastForward,
    0x000c00b4: mediaRewind,
    0x000c00b5: mediaTrackNext,
    0x000c00b6: mediaTrackPrevious,
    0x000c00b7: mediaStop,
    0x000c00b8: eject,
    0x000c00cd: mediaPlayPause,
    0x000c00cf: speechInputToggle,
    0x000c00e5: bassBoost,
    0x000c0183: mediaSelect,
    0x000c0184: launchWordProcessor,
    0x000c0186: launchSpreadsheet,
    0x000c018a: launchMail,
    0x000c018d: launchContacts,
    0x000c018e: launchCalendar,
    0x000c0192: launchApp2,
    0x000c0194: launchApp1,
    0x000c0196: launchInternetBrowser,
    0x000c019c: logOff,
    0x000c019e: lockScreen,
    0x000c019f: launchControlPanel,
    0x000c01a2: selectTask,
    0x000c01a7: launchDocuments,
    0x000c01ab: spellCheck,
    0x000c01ae: launchKeyboardLayout,
    0x000c01b1: launchScreenSaver,
    0x000c01b7: launchAudioBrowser,
    0x000c01cb: launchAssistant,
    0x000c0201: newKey,
    0x000c0203: close,
    0x000c0207: save,
    0x000c0208: print,
    0x000c0221: browserSearch,
    0x000c0223: browserHome,
    0x000c0224: browserBack,
    0x000c0225: browserForward,
    0x000c0226: browserStop,
    0x000c0227: browserRefresh,
    0x000c022a: browserFavorites,
    0x000c022d: zoomIn,
    0x000c022e: zoomOut,
    0x000c0232: zoomToggle,
    0x000c0279: redo,
    0x000c0289: mailReply,
    0x000c028b: mailForward,
    0x000c028c: mailSend,
    0x000c029d: keyboardLayoutSelect,
    0x000c029f: showAllWindows,
  };

  static const Map<int, String> _debugNames =
      kReleaseMode
          ? <int, String>{}
          : <int, String>{
            0x00000010: 'Hyper',
            0x00000011: 'Super Key',
            0x00000012: 'Fn',
            0x00000013: 'Fn Lock',
            0x00000014: 'Suspend',
            0x00000015: 'Resume',
            0x00000016: 'Turbo',
            0x00000017: 'Privacy Screen Toggle',
            0x00000018: 'Microphone Mute Toggle',
            0x00010082: 'Sleep',
            0x00010083: 'Wake Up',
            0x000100b5: 'Display Toggle Int Ext',
            0x0005ff01: 'Game Button 1',
            0x0005ff02: 'Game Button 2',
            0x0005ff03: 'Game Button 3',
            0x0005ff04: 'Game Button 4',
            0x0005ff05: 'Game Button 5',
            0x0005ff06: 'Game Button 6',
            0x0005ff07: 'Game Button 7',
            0x0005ff08: 'Game Button 8',
            0x0005ff09: 'Game Button 9',
            0x0005ff0a: 'Game Button 10',
            0x0005ff0b: 'Game Button 11',
            0x0005ff0c: 'Game Button 12',
            0x0005ff0d: 'Game Button 13',
            0x0005ff0e: 'Game Button 14',
            0x0005ff0f: 'Game Button 15',
            0x0005ff10: 'Game Button 16',
            0x0005ff11: 'Game Button A',
            0x0005ff12: 'Game Button B',
            0x0005ff13: 'Game Button C',
            0x0005ff14: 'Game Button Left 1',
            0x0005ff15: 'Game Button Left 2',
            0x0005ff16: 'Game Button Mode',
            0x0005ff17: 'Game Button Right 1',
            0x0005ff18: 'Game Button Right 2',
            0x0005ff19: 'Game Button Select',
            0x0005ff1a: 'Game Button Start',
            0x0005ff1b: 'Game Button Thumb Left',
            0x0005ff1c: 'Game Button Thumb Right',
            0x0005ff1d: 'Game Button X',
            0x0005ff1e: 'Game Button Y',
            0x0005ff1f: 'Game Button Z',
            0x00070000: 'Usb Reserved',
            0x00070001: 'Usb Error Roll Over',
            0x00070002: 'Usb Post Fail',
            0x00070003: 'Usb Error Undefined',
            0x00070004: 'Key A',
            0x00070005: 'Key B',
            0x00070006: 'Key C',
            0x00070007: 'Key D',
            0x00070008: 'Key E',
            0x00070009: 'Key F',
            0x0007000a: 'Key G',
            0x0007000b: 'Key H',
            0x0007000c: 'Key I',
            0x0007000d: 'Key J',
            0x0007000e: 'Key K',
            0x0007000f: 'Key L',
            0x00070010: 'Key M',
            0x00070011: 'Key N',
            0x00070012: 'Key O',
            0x00070013: 'Key P',
            0x00070014: 'Key Q',
            0x00070015: 'Key R',
            0x00070016: 'Key S',
            0x00070017: 'Key T',
            0x00070018: 'Key U',
            0x00070019: 'Key V',
            0x0007001a: 'Key W',
            0x0007001b: 'Key X',
            0x0007001c: 'Key Y',
            0x0007001d: 'Key Z',
            0x0007001e: 'Digit 1',
            0x0007001f: 'Digit 2',
            0x00070020: 'Digit 3',
            0x00070021: 'Digit 4',
            0x00070022: 'Digit 5',
            0x00070023: 'Digit 6',
            0x00070024: 'Digit 7',
            0x00070025: 'Digit 8',
            0x00070026: 'Digit 9',
            0x00070027: 'Digit 0',
            0x00070028: 'Enter',
            0x00070029: 'Escape',
            0x0007002a: 'Backspace',
            0x0007002b: 'Tab',
            0x0007002c: 'Space',
            0x0007002d: 'Minus',
            0x0007002e: 'Equal',
            0x0007002f: 'Bracket Left',
            0x00070030: 'Bracket Right',
            0x00070031: 'Backslash',
            0x00070033: 'Semicolon',
            0x00070034: 'Quote',
            0x00070035: 'Backquote',
            0x00070036: 'Comma',
            0x00070037: 'Period',
            0x00070038: 'Slash',
            0x00070039: 'Caps Lock',
            0x0007003a: 'F1',
            0x0007003b: 'F2',
            0x0007003c: 'F3',
            0x0007003d: 'F4',
            0x0007003e: 'F5',
            0x0007003f: 'F6',
            0x00070040: 'F7',
            0x00070041: 'F8',
            0x00070042: 'F9',
            0x00070043: 'F10',
            0x00070044: 'F11',
            0x00070045: 'F12',
            0x00070046: 'Print Screen',
            0x00070047: 'Scroll Lock',
            0x00070048: 'Pause',
            0x00070049: 'Insert',
            0x0007004a: 'Home',
            0x0007004b: 'Page Up',
            0x0007004c: 'Delete',
            0x0007004d: 'End',
            0x0007004e: 'Page Down',
            0x0007004f: 'Arrow Right',
            0x00070050: 'Arrow Left',
            0x00070051: 'Arrow Down',
            0x00070052: 'Arrow Up',
            0x00070053: 'Num Lock',
            0x00070054: 'Numpad Divide',
            0x00070055: 'Numpad Multiply',
            0x00070056: 'Numpad Subtract',
            0x00070057: 'Numpad Add',
            0x00070058: 'Numpad Enter',
            0x00070059: 'Numpad 1',
            0x0007005a: 'Numpad 2',
            0x0007005b: 'Numpad 3',
            0x0007005c: 'Numpad 4',
            0x0007005d: 'Numpad 5',
            0x0007005e: 'Numpad 6',
            0x0007005f: 'Numpad 7',
            0x00070060: 'Numpad 8',
            0x00070061: 'Numpad 9',
            0x00070062: 'Numpad 0',
            0x00070063: 'Numpad Decimal',
            0x00070064: 'Intl Backslash',
            0x00070065: 'Context Menu',
            0x00070066: 'Power',
            0x00070067: 'Numpad Equal',
            0x00070068: 'F13',
            0x00070069: 'F14',
            0x0007006a: 'F15',
            0x0007006b: 'F16',
            0x0007006c: 'F17',
            0x0007006d: 'F18',
            0x0007006e: 'F19',
            0x0007006f: 'F20',
            0x00070070: 'F21',
            0x00070071: 'F22',
            0x00070072: 'F23',
            0x00070073: 'F24',
            0x00070074: 'Open',
            0x00070075: 'Help',
            0x00070077: 'Select',
            0x00070079: 'Again',
            0x0007007a: 'Undo',
            0x0007007b: 'Cut',
            0x0007007c: 'Copy',
            0x0007007d: 'Paste',
            0x0007007e: 'Find',
            0x0007007f: 'Audio Volume Mute',
            0x00070080: 'Audio Volume Up',
            0x00070081: 'Audio Volume Down',
            0x00070085: 'Numpad Comma',
            0x00070087: 'Intl Ro',
            0x00070088: 'Kana Mode',
            0x00070089: 'Intl Yen',
            0x0007008a: 'Convert',
            0x0007008b: 'Non Convert',
            0x00070090: 'Lang 1',
            0x00070091: 'Lang 2',
            0x00070092: 'Lang 3',
            0x00070093: 'Lang 4',
            0x00070094: 'Lang 5',
            0x0007009b: 'Abort',
            0x000700a3: 'Props',
            0x000700b6: 'Numpad Paren Left',
            0x000700b7: 'Numpad Paren Right',
            0x000700bb: 'Numpad Backspace',
            0x000700d0: 'Numpad Memory Store',
            0x000700d1: 'Numpad Memory Recall',
            0x000700d2: 'Numpad Memory Clear',
            0x000700d3: 'Numpad Memory Add',
            0x000700d4: 'Numpad Memory Subtract',
            0x000700d7: 'Numpad Sign Change',
            0x000700d8: 'Numpad Clear',
            0x000700d9: 'Numpad Clear Entry',
            0x000700e0: 'Control Left',
            0x000700e1: 'Shift Left',
            0x000700e2: 'Alt Left',
            0x000700e3: 'Meta Left',
            0x000700e4: 'Control Right',
            0x000700e5: 'Shift Right',
            0x000700e6: 'Alt Right',
            0x000700e7: 'Meta Right',
            0x000c0060: 'Info',
            0x000c0061: 'Closed Caption Toggle',
            0x000c006f: 'Brightness Up',
            0x000c0070: 'Brightness Down',
            0x000c0072: 'Brightness Toggle',
            0x000c0073: 'Brightness Minimum',
            0x000c0074: 'Brightness Maximum',
            0x000c0075: 'Brightness Auto',
            0x000c0079: 'Kbd Illum Up',
            0x000c007a: 'Kbd Illum Down',
            0x000c0083: 'Media Last',
            0x000c008c: 'Launch Phone',
            0x000c008d: 'Program Guide',
            0x000c0094: 'Exit',
            0x000c009c: 'Channel Up',
            0x000c009d: 'Channel Down',
            0x000c00b0: 'Media Play',
            0x000c00b1: 'Media Pause',
            0x000c00b2: 'Media Record',
            0x000c00b3: 'Media Fast Forward',
            0x000c00b4: 'Media Rewind',
            0x000c00b5: 'Media Track Next',
            0x000c00b6: 'Media Track Previous',
            0x000c00b7: 'Media Stop',
            0x000c00b8: 'Eject',
            0x000c00cd: 'Media Play Pause',
            0x000c00cf: 'Speech Input Toggle',
            0x000c00e5: 'Bass Boost',
            0x000c0183: 'Media Select',
            0x000c0184: 'Launch Word Processor',
            0x000c0186: 'Launch Spreadsheet',
            0x000c018a: 'Launch Mail',
            0x000c018d: 'Launch Contacts',
            0x000c018e: 'Launch Calendar',
            0x000c0192: 'Launch App2',
            0x000c0194: 'Launch App1',
            0x000c0196: 'Launch Internet Browser',
            0x000c019c: 'Log Off',
            0x000c019e: 'Lock Screen',
            0x000c019f: 'Launch Control Panel',
            0x000c01a2: 'Select Task',
            0x000c01a7: 'Launch Documents',
            0x000c01ab: 'Spell Check',
            0x000c01ae: 'Launch Keyboard Layout',
            0x000c01b1: 'Launch Screen Saver',
            0x000c01b7: 'Launch Audio Browser',
            0x000c01cb: 'Launch Assistant',
            0x000c0201: 'New Key',
            0x000c0203: 'Close',
            0x000c0207: 'Save',
            0x000c0208: 'Print',
            0x000c0221: 'Browser Search',
            0x000c0223: 'Browser Home',
            0x000c0224: 'Browser Back',
            0x000c0225: 'Browser Forward',
            0x000c0226: 'Browser Stop',
            0x000c0227: 'Browser Refresh',
            0x000c022a: 'Browser Favorites',
            0x000c022d: 'Zoom In',
            0x000c022e: 'Zoom Out',
            0x000c0232: 'Zoom Toggle',
            0x000c0279: 'Redo',
            0x000c0289: 'Mail Reply',
            0x000c028b: 'Mail Forward',
            0x000c028c: 'Mail Send',
            0x000c029d: 'Keyboard Layout Select',
            0x000c029f: 'Show All Windows',
          };
}
